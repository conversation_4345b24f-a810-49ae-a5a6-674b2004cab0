/**
 * 工具函数模块
 * 提供游戏中使用的通用工具函数和常量定义
 */

// 游戏常量
const GAME_CONSTANTS = {
    // 花色
    SUITS: {
        SPADES: '♠',
        HEARTS: '♥',
        DIAMONDS: '♦',
        CLUBS: '♣'
    },
    
    // 牌面值
    RANKS: ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'],
    
    // 下注类型
    BET_TYPES: {
        BANKER: 'banker',
        PLAYER: 'player',
        TIE: 'tie',
        BANKER_PAIR: 'banker-pair',
        PLAYER_PAIR: 'player-pair',
        LUCKY6: 'lucky6',
        LUCKY7: 'lucky7'
    },
    
    // 赔率
    PAYOUTS: {
        banker: 1,
        player: 1,
        tie: 8,
        'banker-pair': 11,
        'player-pair': 11,
        lucky6: { twoCards: 12, threeCards: 20 },
        lucky7: { fourCards: 6, fiveOrMore: 15, superLucky: { fourCards: 30, fiveCards: 40, sixCards: 100 } }
    },
    
    // 游戏阶段
    GAME_PHASES: {
        BETTING: 'betting',
        DEALING: 'dealing',
        REVEALING: 'revealing',
        RESULT: 'result'
    },
    
    // 倒计时时间（秒）
    BETTING_TIME: 15,
    
    // 牌组数量
    DECK_COUNT: 8,
    
    // 最小重洗牌数
    MIN_CARDS_FOR_RESHUFFLE: 20
};

// 工具函数类
class Utils {
    /**
     * 生成随机数
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {number} 随机数
     */
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    /**
     * 洗牌算法 (Fisher-Yates)
     * @param {Array} array 要洗牌的数组
     * @returns {Array} 洗牌后的数组
     */
    static shuffle(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    /**
     * 计算牌的点数
     * @param {string} rank 牌面值
     * @returns {number} 点数
     */
    static getCardValue(rank) {
        if (rank === 'A') return 1;
        if (['J', 'Q', 'K'].includes(rank)) return 0;
        return parseInt(rank);
    }
    
    /**
     * 计算手牌总点数
     * @param {Array} cards 手牌数组
     * @returns {number} 总点数（取个位数）
     */
    static calculateHandValue(cards) {
        const total = cards.reduce((sum, card) => sum + this.getCardValue(card.rank), 0);
        return total % 10;
    }
    
    /**
     * 检查是否为对子
     * @param {Array} cards 手牌数组（前两张）
     * @returns {boolean} 是否为对子
     */
    static isPair(cards) {
        if (cards.length < 2) return false;
        return cards[0].rank === cards[1].rank;
    }
    
    /**
     * 格式化货币显示
     * @param {number} amount 金额
     * @returns {string} 格式化后的金额字符串
     */
    static formatCurrency(amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'decimal',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
    
    /**
     * 延迟执行
     * @param {number} ms 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 添加CSS类
     * @param {HTMLElement} element DOM元素
     * @param {string} className 类名
     */
    static addClass(element, className) {
        if (element && className) {
            element.classList.add(className);
        }
    }
    
    /**
     * 移除CSS类
     * @param {HTMLElement} element DOM元素
     * @param {string} className 类名
     */
    static removeClass(element, className) {
        if (element && className) {
            element.classList.remove(className);
        }
    }
    
    /**
     * 切换CSS类
     * @param {HTMLElement} element DOM元素
     * @param {string} className 类名
     */
    static toggleClass(element, className) {
        if (element && className) {
            element.classList.toggle(className);
        }
    }
    
    /**
     * 创建DOM元素
     * @param {string} tag 标签名
     * @param {Object} attributes 属性对象
     * @param {string} textContent 文本内容
     * @returns {HTMLElement} 创建的DOM元素
     */
    static createElement(tag, attributes = {}, textContent = '') {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(key => {
            if (key === 'className') {
                element.className = attributes[key];
            } else if (key === 'dataset') {
                Object.keys(attributes[key]).forEach(dataKey => {
                    element.dataset[dataKey] = attributes[key][dataKey];
                });
            } else {
                element.setAttribute(key, attributes[key]);
            }
        });
        
        if (textContent) {
            element.textContent = textContent;
        }
        
        return element;
    }
    
    /**
     * 显示通知
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, warning)
     * @param {number} duration 显示时长（毫秒）
     */
    static showNotification(message, type = 'success', duration = 3000) {
        const notification = this.createElement('div', {
            className: `notification ${type}`
        }, message);
        
        document.body.appendChild(notification);
        
        // 触发显示动画
        setTimeout(() => {
            this.addClass(notification, 'show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            this.removeClass(notification, 'show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }
    
    /**
     * 深拷贝对象
     * @param {Object} obj 要拷贝的对象
     * @returns {Object} 拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            Object.keys(obj).forEach(key => {
                clonedObj[key] = this.deepClone(obj[key]);
            });
            return clonedObj;
        }
    }
    
    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * 获取元素相对于页面的位置
     * @param {HTMLElement} element DOM元素
     * @returns {Object} 位置信息 {x, y, width, height}
     */
    static getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY,
            width: rect.width,
            height: rect.height
        };
    }
    
    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    static isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    /**
     * 播放音效（如果支持）
     * @param {string} soundName 音效名称
     */
    static playSound(soundName) {
        // 这里可以扩展音效播放功能
        console.log(`Playing sound: ${soundName}`);
    }
}

// 导出常量和工具类
window.GAME_CONSTANTS = GAME_CONSTANTS;
window.Utils = Utils;
