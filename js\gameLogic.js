/**
 * 游戏逻辑模块
 * 实现百家乐游戏的核心逻辑，包括牌组管理、发牌、补牌规则、胜负判定等
 */

class GameLogic {
    constructor() {
        this.deck = [];
        this.usedCards = [];
        this.currentGame = null;
        this.initializeDeck();
    }

    /**
     * 初始化牌组（8副牌）
     */
    initializeDeck() {
        this.deck = [];
        this.usedCards = [];

        // 创建8副牌
        for (let deckNum = 0; deckNum < GAME_CONSTANTS.DECK_COUNT; deckNum++) {
            for (const suit of Object.values(GAME_CONSTANTS.SUITS)) {
                for (const rank of GAME_CONSTANTS.RANKS) {
                    this.deck.push({
                        suit: suit,
                        rank: rank,
                        value: Utils.getCardValue(rank),
                        id: `${suit}${rank}_${deckNum}`
                    });
                }
            }
        }

        // 洗牌
        this.shuffleDeck();
    }

    /**
     * 洗牌
     */
    shuffleDeck() {
        this.deck = Utils.shuffle(this.deck);
        console.log('牌组已洗牌，共', this.deck.length, '张牌');
    }

    /**
     * 检查是否需要重新洗牌
     * @returns {boolean} 是否需要重新洗牌
     */
    needReshuffle() {
        return this.deck.length < GAME_CONSTANTS.MIN_CARDS_FOR_RESHUFFLE;
    }

    /**
     * 发一张牌
     * @returns {Object|null} 发出的牌，如果没有牌则返回null
     */
    dealCard() {
        if (this.needReshuffle()) {
            this.initializeDeck();
        }

        if (this.deck.length === 0) {
            console.error('牌组为空，无法发牌');
            return null;
        }

        const card = this.deck.pop();
        this.usedCards.push(card);
        return card;
    }

    /**
     * 开始新游戏
     * @returns {Object} 游戏状态对象
     */
    startNewGame() {
        this.currentGame = {
            bankerCards: [],
            playerCards: [],
            bankerPoints: 0,
            playerPoints: 0,
            winner: null,
            isNatural: false,
            bankerPair: false,
            playerPair: false,
            totalCards: 0,
            gamePhase: GAME_CONSTANTS.GAME_PHASES.DEALING
        };

        return this.currentGame;
    }

    /**
     * 发初始两张牌
     */
    dealInitialCards() {
        if (!this.currentGame) {
            throw new Error('游戏未开始');
        }

        // 按照百家乐规则发牌：闲家第一张，庄家第一张，闲家第二张，庄家第二张
        this.currentGame.playerCards.push(this.dealCard());
        this.currentGame.bankerCards.push(this.dealCard());
        this.currentGame.playerCards.push(this.dealCard());
        this.currentGame.bankerCards.push(this.dealCard());

        // 计算初始点数
        this.updatePoints();

        // 检查对子
        this.checkPairs();

        // 检查自然赢
        this.checkNatural();

        this.currentGame.totalCards = 4;
    }

    /**
     * 更新点数
     */
    updatePoints() {
        this.currentGame.bankerPoints = Utils.calculateHandValue(this.currentGame.bankerCards);
        this.currentGame.playerPoints = Utils.calculateHandValue(this.currentGame.playerCards);
    }

    /**
     * 检查对子
     */
    checkPairs() {
        this.currentGame.bankerPair = Utils.isPair(this.currentGame.bankerCards);
        this.currentGame.playerPair = Utils.isPair(this.currentGame.playerCards);
    }

    /**
     * 检查自然赢（8点或9点）
     * @returns {boolean} 是否为自然赢
     */
    checkNatural() {
        const bankerNatural = this.currentGame.bankerPoints >= 8;
        const playerNatural = this.currentGame.playerPoints >= 8;

        this.currentGame.isNatural = bankerNatural || playerNatural;

        if (this.currentGame.isNatural) {
            this.determineWinner();
            return true;
        }

        return false;
    }

    /**
     * 执行补牌规则
     */
    executeDrawingRules() {
        if (this.currentGame.isNatural) {
            return; // 自然赢不需要补牌
        }

        let playerThirdCard = null;

        // 闲家补牌规则
        if (this.currentGame.playerPoints <= 5) {
            playerThirdCard = this.dealCard();
            this.currentGame.playerCards.push(playerThirdCard);
            this.currentGame.totalCards++;
            this.updatePoints();
        }

        // 庄家补牌规则
        const shouldBankerDraw = this.shouldBankerDraw(playerThirdCard);
        if (shouldBankerDraw) {
            this.currentGame.bankerCards.push(this.dealCard());
            this.currentGame.totalCards++;
            this.updatePoints();
        }

        // 确定胜负
        this.determineWinner();
    }

    /**
     * 判断庄家是否应该补牌
     * @param {Object|null} playerThirdCard 闲家第三张牌
     * @returns {boolean} 是否应该补牌
     */
    shouldBankerDraw(playerThirdCard) {
        const bankerPoints = this.currentGame.bankerPoints;

        // 如果闲家没有补牌
        if (!playerThirdCard) {
            return bankerPoints <= 5;
        }

        // 如果闲家有补牌，根据庄家点数和闲家第三张牌决定
        const playerThirdValue = playerThirdCard.value;

        switch (bankerPoints) {
            case 0:
            case 1:
            case 2:
                return true;
            case 3:
                return playerThirdValue !== 8;
            case 4:
                return [2, 3, 4, 5, 6, 7].includes(playerThirdValue);
            case 5:
                return [4, 5, 6, 7].includes(playerThirdValue);
            case 6:
                return [6, 7].includes(playerThirdValue);
            case 7:
                return false;
            default:
                return false;
        }
    }

    /**
     * 确定胜负
     */
    determineWinner() {
        if (this.currentGame.bankerPoints > this.currentGame.playerPoints) {
            this.currentGame.winner = 'banker';
        } else if (this.currentGame.playerPoints > this.currentGame.bankerPoints) {
            this.currentGame.winner = 'player';
        } else {
            this.currentGame.winner = 'tie';
        }

        this.currentGame.gamePhase = GAME_CONSTANTS.GAME_PHASES.RESULT;
    }

    /**
     * 计算边注结果
     * @returns {Object} 边注结果
     */
    calculateSideBets() {
        const results = {
            bankerPair: this.currentGame.bankerPair,
            playerPair: this.currentGame.playerPair,
            lucky6: this.checkLucky6(),
            lucky7: this.checkLucky7()
        };

        return results;
    }

    /**
     * 检查幸运6
     * @returns {Object} 幸运6结果
     */
    checkLucky6() {
        if (this.currentGame.winner !== 'banker' || this.currentGame.bankerPoints !== 6) {
            return { win: false };
        }

        const cardCount = this.currentGame.bankerCards.length;
        return {
            win: true,
            type: cardCount === 2 ? 'twoCards' : 'threeCards',
            payout: cardCount === 2 ? 12 : 20
        };
    }

    /**
     * 检查幸运7
     * @returns {Object} 幸运7结果
     */
    checkLucky7() {
        // 普通幸运7：闲家7点获胜
        if (this.currentGame.winner === 'player' && this.currentGame.playerPoints === 7) {
            const totalCards = this.currentGame.totalCards;

            // 超级幸运7：闲家7点赢庄家6点
            if (this.currentGame.bankerPoints === 6) {
                let payout;
                if (totalCards === 4) payout = 30;
                else if (totalCards === 5) payout = 40;
                else if (totalCards === 6) payout = 100;
                else payout = 0;

                return {
                    win: true,
                    type: 'superLucky',
                    payout: payout
                };
            }

            // 普通幸运7
            if (totalCards >= 4) {
                const payout = totalCards === 4 ? 6 : 15;
                return {
                    win: true,
                    type: 'normal',
                    payout: payout
                };
            }
        }

        return { win: false };
    }

    /**
     * 获取当前游戏状态
     * @returns {Object} 游戏状态
     */
    getCurrentGame() {
        return this.currentGame ? Utils.deepClone(this.currentGame) : null;
    }

    /**
     * 获取牌组信息
     * @returns {Object} 牌组信息
     */
    getDeckInfo() {
        return {
            remainingCards: this.deck.length,
            usedCards: this.usedCards.length,
            needReshuffle: this.needReshuffle()
        };
    }

    /**
     * 重置游戏
     */
    resetGame() {
        this.currentGame = null;
    }

    /**
     * 获取牌的显示信息
     * @param {Object} card 牌对象
     * @returns {Object} 显示信息
     */
    getCardDisplayInfo(card) {
        if (!card) return null;

        return {
            rank: card.rank,
            suit: card.suit,
            value: card.value,
            color: [GAME_CONSTANTS.SUITS.HEARTS, GAME_CONSTANTS.SUITS.DIAMONDS].includes(card.suit) ? 'red' : 'black',
            displayText: `${card.rank}${card.suit}`,
            imagePath: this.getCardImagePath(card)
        };
    }

    /**
     * 获取卡牌PNG图片路径
     * @param {Object} card 卡牌对象
     * @returns {string} PNG文件路径
     */
    getCardImagePath(card) {
        if (!card) return '';

        // 将花色转换为PNG文件名格式
        const suitMap = {
            'hearts': 'H',
            'diamonds': 'D',
            'clubs': 'C',
            'spades': 'S'
        };

        // 将点数转换为PNG文件名格式
        const rankMap = {
            1: 'A',
            11: 'J',
            12: 'Q',
            13: 'K'
        };

        const rank = rankMap[card.rank] || card.rank.toString();
        const suit = suitMap[card.suit];

        return `PNG/${rank}${suit}.png`;
    }
}

// 创建全局游戏逻辑实例
window.gameLogic = new GameLogic();
