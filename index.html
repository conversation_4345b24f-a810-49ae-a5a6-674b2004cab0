<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百家乐游戏</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/table.css">
    <link rel="stylesheet" href="css/cards.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <div id="game-container">
        <!-- 游戏头部信息 -->
        <header class="game-header">
            <div class="player-info">
                <span class="balance-label">余额:</span>
                <span id="player-balance" class="balance-amount">10000</span>
            </div>
            <div class="game-status">
                <span id="game-phase">等待下注</span>
                <span id="countdown-timer" class="timer">15</span>
            </div>
        </header>

        <!-- 主游戏区域 -->
        <main class="game-main">
            <!-- 牌桌区域 -->
            <section class="table-section">
                <div class="card-area">
                    <!-- 庄家牌区 -->
                    <div class="banker-area">
                        <h3>庄家</h3>
                        <div class="card-slots" id="banker-cards">
                            <div class="card-slot"></div>
                            <div class="card-slot"></div>
                            <div class="card-slot"></div>
                        </div>
                        <div class="points" id="banker-points">0</div>
                    </div>

                    <!-- 闲家牌区 -->
                    <div class="player-area">
                        <h3>闲家</h3>
                        <div class="card-slots" id="player-cards">
                            <div class="card-slot"></div>
                            <div class="card-slot"></div>
                            <div class="card-slot"></div>
                        </div>
                        <div class="points" id="player-points">0</div>
                    </div>
                </div>

                <!-- 下注区域 -->
                <div class="betting-area">
                    <!-- 主要下注区 -->
                    <div class="main-bets">
                        <div class="bet-zone" data-bet="banker">
                            <div class="bet-label">庄家</div>
                            <div class="bet-odds">1赔1</div>
                            <div class="bet-amount" id="banker-bet">0</div>
                        </div>
                        <div class="bet-zone" data-bet="player">
                            <div class="bet-label">闲家</div>
                            <div class="bet-odds">1赔1</div>
                            <div class="bet-amount" id="player-bet">0</div>
                        </div>
                        <div class="bet-zone" data-bet="tie">
                            <div class="bet-label">和局</div>
                            <div class="bet-odds">1赔8</div>
                            <div class="bet-amount" id="tie-bet">0</div>
                        </div>
                    </div>

                    <!-- 边注区域 -->
                    <div class="side-bets">
                        <div class="bet-zone" data-bet="banker-pair">
                            <div class="bet-label">庄对</div>
                            <div class="bet-odds">1赔11</div>
                            <div class="bet-amount" id="banker-pair-bet">0</div>
                        </div>
                        <div class="bet-zone" data-bet="player-pair">
                            <div class="bet-label">闲对</div>
                            <div class="bet-odds">1赔11</div>
                            <div class="bet-amount" id="player-pair-bet">0</div>
                        </div>
                        <div class="bet-zone" data-bet="lucky6">
                            <div class="bet-label">幸运6</div>
                            <div class="bet-odds">1赔12/20</div>
                            <div class="bet-amount" id="lucky6-bet">0</div>
                        </div>
                        <div class="bet-zone" data-bet="lucky7">
                            <div class="bet-label">幸运7</div>
                            <div class="bet-odds">1赔6/15</div>
                            <div class="bet-amount" id="lucky7-bet">0</div>
                        </div>
                    </div>
                </div>

                <!-- 筹码选择区 -->
                <div class="chip-selection">
                    <div class="chip" data-value="10">10</div>
                    <div class="chip" data-value="50">50</div>
                    <div class="chip" data-value="100">100</div>
                    <div class="chip" data-value="500">500</div>
                    <div class="chip" data-value="1000">1000</div>
                    <button id="clear-bets" class="clear-btn">清除下注</button>
                </div>
            </section>

        </main>

        <!-- 路单区域 -->
        <section class="roadmap-section">
            <div class="roadmap-container">
                <!-- 左侧：珠盘路 -->
                <div class="roadmap-left">
                    <div class="roadmap-panel">
                        <h4>珠盘路</h4>
                        <div id="bead-road" class="bead-road"></div>
                    </div>
                </div>

                <!-- 中间：大路、小路、大眼仔路、曱甴路 -->
                <div class="roadmap-center">
                    <!-- 上方：大路 -->
                    <div class="roadmap-panel roadmap-big">
                        <h4>大路</h4>
                        <div id="big-road" class="big-road"></div>
                    </div>

                    <!-- 中间：小路 -->
                    <div class="roadmap-panel roadmap-small">
                        <h4>小路</h4>
                        <div id="small-road" class="small-road"></div>
                    </div>

                    <!-- 下方：大眼仔路和曱甴路 -->
                    <div class="roadmap-bottom">
                        <div class="roadmap-panel roadmap-eye">
                            <h4>大眼仔路</h4>
                            <div id="big-eye-road" class="big-eye-road"></div>
                        </div>
                        <div class="roadmap-panel roadmap-cockroach">
                            <h4>曱甴路</h4>
                            <div id="cockroach-road" class="cockroach-road"></div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：统计信息 -->
                <div class="roadmap-right">
                    <div class="statistics-panel">
                        <h4>统计</h4>
                        <div class="stats">
                            <div class="stat-item">
                                <span>庄:</span>
                                <span id="banker-wins">0</span>
                            </div>
                            <div class="stat-item">
                                <span>闲:</span>
                                <span id="player-wins">0</span>
                            </div>
                            <div class="stat-item">
                                <span>和:</span>
                                <span id="tie-wins">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <main class="game-main" style="display: none;">
        </main>

        <!-- 结果弹窗 -->
        <div id="result-modal" class="modal hidden">
            <div class="modal-content">
                <h2 id="result-title">游戏结果</h2>
                <div id="result-details"></div>
                <div id="payout-info"></div>
                <button id="next-round" class="btn-primary">下一局</button>
            </div>
        </div>

        <!-- 游戏规则弹窗 -->
        <div id="rules-modal" class="modal hidden">
            <div class="modal-content">
                <h2>游戏规则</h2>
                <div class="rules-content">
                    <h3>基本规则</h3>
                    <ul>
                        <li><strong>牌面点数：</strong>A=1点，2-9按面值，10/J/Q/K=0点</li>
                        <li><strong>计分方式：</strong>两张牌点数相加，取个位数</li>
                        <li><strong>自然赢：</strong>前两张牌总点数为8或9点</li>
                        <li><strong>补牌规则：</strong>根据标准百家乐补牌规则自动执行</li>
                    </ul>

                    <h3>下注类型</h3>
                    <ul>
                        <li><strong>庄家：</strong>庄家点数大于闲家，1赔1（6点获胜时1赔0.5）</li>
                        <li><strong>闲家：</strong>闲家点数大于庄家，1赔1</li>
                        <li><strong>和局：</strong>双方点数相同，1赔8</li>
                        <li><strong>庄对/闲对：</strong>前两张牌组成对子，1赔11</li>
                        <li><strong>幸运6：</strong>庄家以6点获胜，两张牌1赔12，三张牌1赔20</li>
                        <li><strong>幸运7：</strong>闲家以7点获胜，根据总牌数不同赔率</li>
                    </ul>

                    <h3>操作说明</h3>
                    <ul>
                        <li>选择筹码面值，点击下注区域进行下注</li>
                        <li>15秒倒计时结束后自动发牌</li>
                        <li>使用数字键1-5快速选择筹码</li>
                        <li>按空格键清除所有下注</li>
                    </ul>
                </div>
                <button id="close-rules" class="btn-secondary">关闭</button>
            </div>
        </div>

        <!-- 底部控制栏 -->
        <footer class="game-footer">
            <button id="rules-btn" class="btn-secondary">游戏规则</button>
            <button id="history-btn" class="btn-secondary">历史记录</button>
            <button id="settings-btn" class="btn-secondary">设置</button>
            <button id="reset-balance-btn" class="btn-secondary">重置余额</button>
            <button id="add-balance-btn" class="btn-secondary">增加余额</button>
            <button id="test-cards-btn" class="btn-secondary">测试卡牌</button>
        </footer>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/dataStorage.js"></script>
    <script src="js/gameLogic.js"></script>
    <script src="js/stateManager.js"></script>
    <script src="js/animationManager.js"></script>
    <script src="js/uiManager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
