开发一款HTML+CSS+JavaSrcipt的网页版单机百家乐游戏，支持标准玩法及主流变种，以下是全部的项目说明。

建议：先把游戏内容所有模块单独做好框架，最后再一个一个的把所有模块调用、引入、关联起来。根目录PNG文件夹里面已经提供了全套扑克牌的图案。

一、玩法说明
根据澳门百家乐玩法，百家乐的游戏目标是猜测庄家和闲家中哪一方的牌点数总和更大为获胜者。
游戏开始时，游戏系统会自动发牌，庄家和闲家各获得两张牌，然后根据庄家和闲家的点数判断是否需要补牌，然后再判断胜负。（所有发牌都是系统自动）
牌面点数计算：A牌记为1点。10、J、Q、K牌记为0点。其余牌按面值计算，即2至9点牌分别计为2至9点。
若两张牌总点数超过10，则仅取个位数作为该手的点数。例如，一张8和一张7的总和为15，实际点数为5。

游戏流程：
  用户->>界面: 1. 「自动开始游戏倒计时15秒」
  界面->>逻辑核心: 初始化牌局
  逻辑核心->>数据存储: 加载用户余额
  逻辑核心-->>界面: 渲染牌桌（空牌位+筹码区）
  
  用户->>界面: 2. 选择筹码并下注
  界面->>逻辑核心: 验证余额是否充足
  逻辑核心->>数据存储: 冻结下注金额
  界面-->>用户: 显示下注筹码动画
  
  用户->>界面: 3. 「倒计时15秒结束自动发牌」
  界面->>逻辑核心: 触发发牌逻辑
  逻辑核心->>逻辑核心: 执行补牌规则
  逻辑核心->>界面: 推送发牌动画指令
  界面->>用户: 播放发牌动画（卡牌翻转+音效）
  
  逻辑核心->>逻辑核心: 计算胜负
  逻辑核心->>数据存储: 更新余额与历史记录
  逻辑核心->>界面: 推送结算结果
  界面->>用户: 显示胜负弹窗+更新路单

二、投注和赔率介绍
庄家（Banker） - 获胜条件：庄家点数 > 闲家点数（1赔1，庄以6点获胜1赔0.5）
闲家（Player） - 获胜条件：闲家点数 > 庄家点数（1赔1）
和局（Tie） - 获胜条件：双方点数相同（1赔8）
庄对/闲对（Banker Pair/Player Pair） - 获胜条件：庄家或闲家的 前两张牌 组成对子（如两张Q、两张5）（1赔11），仅判断前两张牌，与胜负无关，若同时押中庄对+闲对，可双重派彩
幸运6（Lucky 6） - 获胜条件：庄家两张牌总点数为6时，赔付比例为1赔12。庄家三张牌总点数为6时，赔付比例为1赔20。
幸运7（Lucky 7）有两种获胜条件
第一种获胜条件：当闲家（非庄家）手中的点数之和为7，并且此局中庄闲牌张总数是四张或五张以上（含五张）时，单独压中“幸运七”者即可中奖。
若庄闲牌张总数为四张，获赔压注额的6倍。
若庄闲牌张总数为五张及以上，获赔压注额的15倍。
第二种获胜条件：闲家以7点赢庄家6点，并且庄闲牌张总数满足特定条件时，单独压中“超级幸运七”者即可中奖。
若庄闲牌张总数为四张，获赔压注额的30倍。
若庄闲牌张总数为五张，获赔压注额的40倍。
若庄闲牌张总数为六张，获赔压注额的100倍。

三、游戏核心逻辑
牌局管理
 - 8副牌洗牌算法（Fisher-Yates随机化）
 - 牌堆剩余牌数追踪与自动重洗
 - 发牌顺序控制（闲/庄交替发牌）

规则引擎
 - 点数计算（10/J/Q/K=0，A=1）
 - 第三张牌规则自动触发判断补牌
 - 自然赢（Natural Win）优先判定

下注系统
 - 注区定义（庄/闲/和/庄对/闲对/幸运6/幸运7）
 - 15秒下注倒计时同步（前端与服务器校准）

派彩逻辑
 - 各注区赔率配置表
 - 自动结算与余额更新（原子操作防并发问题）

数据面板
 - 路单绘制（参考路单图示例.png，珠盘路、大路、小路、大眼仔路、曱甴路）
 注意：珠盘路在最左侧，中间区域采用上中下的布局，大路在中间上边，小路在中间的中间，大眼仔路在中间的左下，曱甴路在中间的右下，最右侧是统计庄闲和等等的数据
 - 每步操作余额实时更新
 - 历史记录分页加载与筛选

发牌轨迹
 - 发牌路径生成（庄/闲位置插值算法）
 - 物理模拟发牌
 - 卡片惯性滑动（缓动函数实现）

翻转动画
 - 背面图案到正面牌面的3D翻转动画
 - 音效同步，发牌摩擦声与落桌音效的时间校准

四、游戏整体模块化处理
1、用户界面模块：
- 责渲染牌桌和基本布局。
- 处理下注界面的渲染和交互。
- 显示玩家信息，如余额和历史记录。

2、游戏逻辑模块
- 实现牌组的创建和洗牌功能。
- 负责发牌和管理牌局。
- 包含游戏规则和胜负判定逻辑。

3、状态管理模块
- 管理游戏的整体状态（如当前轮次、牌局状态）。
- 管理玩家的状态（如余额、下注金额）。

4、动画与效果模块
- 实现发牌动画。
- 实现下注时的筹码动画。

5、数据存储模块
- 管理本地存储的数据，如用户设置和游戏进度。
- 记录和管理玩家的游戏历史。

6、辅助功能模块
- 提供游戏规则和帮助信息。
- 管理游戏设置（如音效、语言）。

五、技术栈确认：HTML+CSS+JavaSrcipt

六、适合场景
个人学习/演示项目：用于理解百家乐规则和前端基础技术。
快速原型验证：在正式开发前验证界面交互设计。
单机练习模式：用户无需联网即可体验游戏流程。