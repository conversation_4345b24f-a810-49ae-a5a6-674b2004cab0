/* 动画效果样式 */

/* 筹码动画 */
.chip-animation {
    position: absolute;
    pointer-events: none;
    z-index: 100;
}

.chip-flying {
    animation: chipFly 1s ease-out forwards;
}

@keyframes chipFly {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.8) rotate(360deg);
        opacity: 1;
    }
}

/* 筹码堆叠动画 */
.chip-stack {
    animation: chipStack 0.3s ease-out;
}

@keyframes chipStack {
    0% {
        transform: translateY(-20px) scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* 发牌动画 */
.card-dealing {
    animation: cardDeal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes cardDeal {
    0% {
        transform: translateX(-300px) translateY(-150px) rotate(-30deg) scale(0.8);
        opacity: 0;
    }
    30% {
        opacity: 1;
    }
    70% {
        transform: translateX(20px) translateY(10px) rotate(5deg) scale(1.1);
    }
    100% {
        transform: translateX(0) translateY(0) rotate(0deg) scale(1);
        opacity: 1;
    }
}

/* 翻牌动画 */
.card-reveal {
    animation: cardReveal 0.8s ease-in-out;
}

@keyframes cardReveal {
    0% {
        transform: rotateY(0deg) scale(1);
    }
    25% {
        transform: rotateY(45deg) scale(1.05);
    }
    50% {
        transform: rotateY(90deg) scale(1.1);
    }
    75% {
        transform: rotateY(135deg) scale(1.05);
    }
    100% {
        transform: rotateY(180deg) scale(1);
    }
}

/* 胜利动画 */
.victory-animation {
    animation: victory 2s ease-in-out;
}

@keyframes victory {
    0%, 100% {
        transform: scale(1);
        filter: brightness(1);
    }
    25% {
        transform: scale(1.1);
        filter: brightness(1.3);
    }
    50% {
        transform: scale(1.05);
        filter: brightness(1.5);
    }
    75% {
        transform: scale(1.1);
        filter: brightness(1.3);
    }
}

/* 失败动画 */
.defeat-animation {
    animation: defeat 1.5s ease-out;
}

@keyframes defeat {
    0% {
        transform: scale(1);
        filter: brightness(1) grayscale(0);
    }
    50% {
        transform: scale(0.95);
        filter: brightness(0.7) grayscale(0.5);
    }
    100% {
        transform: scale(0.9);
        filter: brightness(0.5) grayscale(0.8);
    }
}

/* 倒计时动画 */
.countdown-urgent {
    animation: countdownUrgent 1s ease-in-out infinite;
}

@keyframes countdownUrgent {
    0%, 100% {
        transform: scale(1);
        color: #ff6b6b;
        text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    }
    50% {
        transform: scale(1.2);
        color: #ff3333;
        text-shadow: 0 0 20px rgba(255, 51, 51, 0.8);
    }
}

/* 金币飞舞动画 */
.coin-animation {
    position: absolute;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #ffd700, #ffed4e);
    border-radius: 50%;
    border: 2px solid #ffb300;
    pointer-events: none;
    z-index: 200;
}

.coin-fly {
    animation: coinFly 2s ease-out forwards;
}

@keyframes coinFly {
    0% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 1;
    }
    25% {
        transform: translateY(-50px) rotate(90deg) scale(1.2);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-80px) rotate(180deg) scale(1.1);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-60px) rotate(270deg) scale(1.3);
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) rotate(360deg) scale(0.5);
        opacity: 0;
    }
}

/* 粒子效果 */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    pointer-events: none;
    z-index: 150;
}

.particle-burst {
    animation: particleBurst 1.5s ease-out forwards;
}

@keyframes particleBurst {
    0% {
        transform: scale(1) translate(0, 0);
        opacity: 1;
    }
    100% {
        transform: scale(0) translate(var(--dx), var(--dy));
        opacity: 0;
    }
}

/* 路单更新动画 */
.roadmap-update {
    animation: roadmapUpdate 0.5s ease-out;
}

@keyframes roadmapUpdate {
    0% {
        transform: scale(0) rotate(45deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(22.5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* 按钮点击动画 */
.button-click {
    animation: buttonClick 0.2s ease-out;
}

@keyframes buttonClick {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* 余额变化动画 */
.balance-increase {
    animation: balanceIncrease 1s ease-out;
}

@keyframes balanceIncrease {
    0% {
        transform: scale(1);
        color: #ffd700;
    }
    50% {
        transform: scale(1.2);
        color: #4CAF50;
        text-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
    }
    100% {
        transform: scale(1);
        color: #ffd700;
    }
}

.balance-decrease {
    animation: balanceDecrease 1s ease-out;
}

@keyframes balanceDecrease {
    0% {
        transform: scale(1);
        color: #ffd700;
    }
    50% {
        transform: scale(0.9);
        color: #f44336;
        text-shadow: 0 0 20px rgba(244, 67, 54, 0.8);
    }
    100% {
        transform: scale(1);
        color: #ffd700;
    }
}

/* 闪烁效果 */
.blink {
    animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

/* 摇摆效果 */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* 弹跳效果 */
.bounce {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    25% {
        transform: translateY(-10px);
    }
    50% {
        transform: translateY(-5px);
    }
    75% {
        transform: translateY(-2px);
    }
}

/* 渐入效果 */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 渐出效果 */
.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* 旋转加载 */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 缩放脉冲 */
.pulse-scale {
    animation: pulseScale 2s ease-in-out infinite;
}

@keyframes pulseScale {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}
