# 百家乐游戏 (Baccarat Game)

一款基于HTML、CSS、JavaScript开发的单机网页版百家乐游戏，支持标准玩法及主流变种。

## 功能特性

### 核心玩法
- ✅ 标准百家乐规则（庄家、闲家、和局）
- ✅ 自动发牌和补牌规则
- ✅ 8副牌洗牌系统
- ✅ 15秒下注倒计时

### 下注选项
- ✅ 主要下注：庄家(1赔1)、闲家(1赔1)、和局(1赔8)
- ✅ 边注：庄对/闲对(1赔11)
- ✅ 特殊玩法：幸运6、幸运7
- ✅ 多种筹码面值：10、50、100、500、1000

### 路单系统
- ✅ 珠盘路
- ✅ 大路
- ✅ 小路、大眼仔路、曱甴路（基础实现）
- ✅ 实时统计信息

### 用户体验
- ✅ 精美的发牌动画
- ✅ 流畅的筹码飞行效果
- ✅ 卡牌翻转动画
- ✅ 响应式设计，支持移动端
- ✅ 本地数据存储

## 技术架构

### 模块化设计
项目采用模块化架构，各模块职责清晰：

```
js/
├── utils.js           # 工具函数和常量定义
├── dataStorage.js     # 数据存储管理
├── gameLogic.js       # 游戏核心逻辑
├── stateManager.js    # 状态管理
├── animationManager.js # 动画效果管理
├── uiManager.js       # 用户界面管理
└── main.js           # 主控制器
```

### 样式文件
```
css/
├── main.css          # 主样式
├── table.css         # 牌桌样式
├── cards.css         # 卡牌样式
└── animations.css    # 动画样式
```

## 游戏规则

### 基本规则
1. **牌面点数**：A=1点，2-9按面值，10/J/Q/K=0点
2. **计分方式**：两张牌点数相加，取个位数
3. **自然赢**：前两张牌总点数为8或9点
4. **补牌规则**：根据标准百家乐补牌规则自动执行

### 下注类型
- **庄家**：庄家点数大于闲家，1赔1（6点获胜时1赔0.5）
- **闲家**：闲家点数大于庄家，1赔1
- **和局**：双方点数相同，1赔8
- **庄对/闲对**：前两张牌组成对子，1赔11
- **幸运6**：庄家以6点获胜，两张牌1赔12，三张牌1赔20
- **幸运7**：闲家以7点获胜，根据总牌数不同赔率

## 使用说明

### 开始游戏
1. 打开 `index.html` 文件
2. 游戏自动开始15秒下注倒计时
3. 选择筹码面值，点击下注区域进行下注
4. 倒计时结束后自动发牌

### 操作指南
- **选择筹码**：点击底部筹码或使用数字键1-5
- **下注**：点击相应的下注区域
- **清除下注**：点击"清除下注"按钮或按空格键
- **查看规则**：点击底部"游戏规则"按钮

### 键盘快捷键
- `1-5`：选择对应筹码
- `空格`：清除所有下注
- `ESC`：关闭模态框

## 数据存储

游戏使用浏览器本地存储保存以下数据：
- 玩家余额
- 游戏历史记录
- 路单数据
- 统计信息
- 用户设置

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 开发说明

### 项目结构
```
baccarat2/
├── index.html          # 主页面
├── css/               # 样式文件
├── js/                # JavaScript模块
├── assets/            # 资源文件（预留）
└── README.md          # 项目说明
```

### 扩展功能
项目预留了以下扩展接口：
- 音效系统
- 多语言支持
- 成就系统
- 历史记录详细查看
- 游戏设置

### 调试工具
在浏览器控制台中可以访问 `window.BaccaratGame` 对象来调试：
```javascript
// 查看游戏状态
BaccaratGame.controller.getGameSummary()

// 访问各个模块
BaccaratGame.logic    // 游戏逻辑
BaccaratGame.state    // 状态管理
BaccaratGame.storage  // 数据存储
```

## 许可证

本项目仅供学习和演示使用。

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 完成基础游戏功能
- ✅ 实现标准百家乐规则
- ✅ 添加动画效果
- ✅ 完成路单系统
- ✅ 实现数据持久化

---

**注意**：本游戏仅供娱乐和学习目的，不涉及真实货币交易。
