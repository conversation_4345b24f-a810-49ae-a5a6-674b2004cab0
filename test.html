<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百家乐游戏测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
            font-family: monospace;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>百家乐游戏测试页面</h1>
        
        <div class="test-section">
            <h2>模块加载测试</h2>
            <button class="test-button" onclick="testModules()">测试模块加载</button>
            <div id="module-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>游戏逻辑测试</h2>
            <button class="test-button" onclick="testGameLogic()">测试游戏逻辑</button>
            <div id="logic-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>数据存储测试</h2>
            <button class="test-button" onclick="testDataStorage()">测试数据存储</button>
            <div id="storage-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>状态管理测试</h2>
            <button class="test-button" onclick="testStateManager()">测试状态管理</button>
            <div id="state-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>完整游戏流程测试</h2>
            <button class="test-button" onclick="testFullGame()">测试完整流程</button>
            <div id="game-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>快速操作</h2>
            <button class="test-button" onclick="window.open('index.html', '_blank')">打开游戏</button>
            <button class="test-button" onclick="resetGameData()">重置游戏数据</button>
            <button class="test-button" onclick="showGameStatus()">显示游戏状态</button>
        </div>
    </div>

    <!-- 加载游戏模块 -->
    <script src="js/utils.js"></script>
    <script src="js/dataStorage.js"></script>
    <script src="js/gameLogic.js"></script>
    <script src="js/stateManager.js"></script>
    <script src="js/animationManager.js"></script>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<div class="${className}">${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function testModules() {
            const resultId = 'module-result';
            document.getElementById(resultId).innerHTML = '';
            
            const modules = ['Utils', 'GAME_CONSTANTS', 'dataStorage', 'gameLogic', 'stateManager', 'animationManager'];
            let allLoaded = true;
            
            modules.forEach(module => {
                if (window[module]) {
                    log(resultId, `✓ ${module} 模块加载成功`, 'success');
                } else {
                    log(resultId, `✗ ${module} 模块加载失败`, 'error');
                    allLoaded = false;
                }
            });
            
            if (allLoaded) {
                log(resultId, '所有模块加载成功！', 'success');
            } else {
                log(resultId, '部分模块加载失败', 'error');
            }
        }

        function testGameLogic() {
            const resultId = 'logic-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                // 测试牌组初始化
                log(resultId, '测试牌组初始化...', 'info');
                gameLogic.initializeDeck();
                const deckInfo = gameLogic.getDeckInfo();
                log(resultId, `牌组初始化成功，共 ${deckInfo.remainingCards} 张牌`, 'success');
                
                // 测试发牌
                log(resultId, '测试发牌功能...', 'info');
                const card = gameLogic.dealCard();
                if (card) {
                    log(resultId, `发牌成功：${card.rank}${card.suit}`, 'success');
                } else {
                    log(resultId, '发牌失败', 'error');
                }
                
                // 测试游戏流程
                log(resultId, '测试游戏流程...', 'info');
                const game = gameLogic.startNewGame();
                gameLogic.dealInitialCards();
                const gameData = gameLogic.getCurrentGame();
                log(resultId, `游戏开始成功，庄家：${gameData.bankerPoints}点，闲家：${gameData.playerPoints}点`, 'success');
                
                // 测试补牌规则
                if (!gameData.isNatural) {
                    gameLogic.executeDrawingRules();
                    const finalData = gameLogic.getCurrentGame();
                    log(resultId, `补牌完成，最终结果：${finalData.winner}获胜`, 'success');
                }
                
                log(resultId, '游戏逻辑测试完成！', 'success');
                
            } catch (error) {
                log(resultId, `游戏逻辑测试失败：${error.message}`, 'error');
            }
        }

        function testDataStorage() {
            const resultId = 'storage-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                // 测试余额操作
                log(resultId, '测试余额操作...', 'info');
                const originalBalance = dataStorage.getPlayerBalance();
                log(resultId, `当前余额：${originalBalance}`, 'info');
                
                dataStorage.updatePlayerBalance(100);
                const newBalance = dataStorage.getPlayerBalance();
                log(resultId, `余额更新成功：${newBalance}`, 'success');
                
                // 测试设置
                log(resultId, '测试设置功能...', 'info');
                dataStorage.setSetting('testSetting', 'testValue');
                const setting = dataStorage.getSetting('testSetting');
                if (setting === 'testValue') {
                    log(resultId, '设置功能正常', 'success');
                } else {
                    log(resultId, '设置功能异常', 'error');
                }
                
                // 测试统计
                log(resultId, '测试统计功能...', 'info');
                const stats = dataStorage.getStatistics();
                log(resultId, `统计数据：总游戏数 ${stats.totalGames}`, 'success');
                
                log(resultId, '数据存储测试完成！', 'success');
                
            } catch (error) {
                log(resultId, `数据存储测试失败：${error.message}`, 'error');
            }
        }

        function testStateManager() {
            const resultId = 'state-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                // 测试状态初始化
                log(resultId, '测试状态管理初始化...', 'info');
                const gameState = stateManager.getGameState();
                const playerState = stateManager.getPlayerState();
                log(resultId, `游戏状态：${gameState.phase}，玩家余额：${playerState.balance}`, 'success');
                
                // 测试下注
                log(resultId, '测试下注功能...', 'info');
                stateManager.setSelectedChip(100);
                const betSuccess = stateManager.placeBet('banker', 100);
                if (betSuccess) {
                    log(resultId, '下注成功', 'success');
                } else {
                    log(resultId, '下注失败', 'error');
                }
                
                // 测试清除下注
                log(resultId, '测试清除下注...', 'info');
                const clearSuccess = stateManager.clearAllBets();
                if (clearSuccess) {
                    log(resultId, '清除下注成功', 'success');
                } else {
                    log(resultId, '清除下注失败', 'error');
                }
                
                log(resultId, '状态管理测试完成！', 'success');
                
            } catch (error) {
                log(resultId, `状态管理测试失败：${error.message}`, 'error');
            }
        }

        function testFullGame() {
            const resultId = 'game-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '开始完整游戏流程测试...', 'info');
                
                // 重置状态
                stateManager.reset();
                gameLogic.resetGame();
                
                // 开始新游戏
                log(resultId, '1. 开始新游戏...', 'info');
                const game = gameLogic.startNewGame();
                stateManager.startNewRound();
                
                // 下注
                log(resultId, '2. 进行下注...', 'info');
                stateManager.setSelectedChip(100);
                stateManager.placeBet('banker', 100);
                stateManager.placeBet('player', 50);
                
                // 发牌
                log(resultId, '3. 发牌...', 'info');
                gameLogic.dealInitialCards();
                const gameData = gameLogic.getCurrentGame();
                log(resultId, `初始牌：庄家${gameData.bankerPoints}点，闲家${gameData.playerPoints}点`, 'info');
                
                // 补牌
                if (!gameData.isNatural) {
                    log(resultId, '4. 执行补牌规则...', 'info');
                    gameLogic.executeDrawingRules();
                }
                
                // 结算
                log(resultId, '5. 游戏结算...', 'info');
                const finalData = gameLogic.getCurrentGame();
                stateManager.endGame(finalData);
                
                log(resultId, `游戏结果：${finalData.winner}获胜！`, 'success');
                log(resultId, '完整游戏流程测试成功！', 'success');
                
            } catch (error) {
                log(resultId, `完整游戏流程测试失败：${error.message}`, 'error');
            }
        }

        function resetGameData() {
            if (confirm('确定要重置所有游戏数据吗？')) {
                dataStorage.resetAllData();
                alert('游戏数据已重置');
            }
        }

        function showGameStatus() {
            const status = {
                balance: dataStorage.getPlayerBalance(),
                stats: dataStorage.getStatistics(),
                deckInfo: gameLogic.getDeckInfo()
            };
            alert(JSON.stringify(status, null, 2));
        }

        // 页面加载完成后自动测试模块
        window.addEventListener('load', () => {
            setTimeout(testModules, 500);
        });
    </script>
</body>
</html>
