/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background: linear-gradient(135deg, #0f4c3a 0%, #1a5f4a 50%, #0f4c3a 100%);
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

#game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 15px 25px;
    border-radius: 10px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.balance-label {
    font-size: 16px;
    color: #cccccc;
}

.balance-amount {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.game-status {
    display: flex;
    align-items: center;
    gap: 15px;
}

#game-phase {
    font-size: 18px;
    color: #ffffff;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.timer {
    font-size: 28px;
    font-weight: bold;
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    padding: 10px 15px;
    border-radius: 50%;
    border: 2px solid #ff6b6b;
    min-width: 60px;
    text-align: center;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 主游戏区域 */
.game-main {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin-bottom: 20px;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: linear-gradient(135deg, #1a5f4a, #0f4c3a);
    padding: 30px;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-content h2 {
    margin-bottom: 20px;
    color: #ffd700;
    text-align: center;
    font-size: 24px;
}

/* 底部控制栏 */
.game-footer {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 路单区域样式 */
.roadmap-section {
    background: linear-gradient(135deg, #1a5f4a, #0f4c3a);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    margin-top: 20px;
}

.roadmap-container {
    display: flex;
    gap: 20px;
    height: 400px;
}

/* 左侧：珠盘路 */
.roadmap-left {
    flex: 0 0 200px;
}

/* 中间：大路、小路、大眼仔路、曱甴路 */
.roadmap-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.roadmap-big {
    flex: 2;
}

.roadmap-small {
    flex: 1;
}

.roadmap-bottom {
    flex: 1;
    display: flex;
    gap: 10px;
}

.roadmap-eye,
.roadmap-cockroach {
    flex: 1;
}

/* 右侧：统计信息 */
.roadmap-right {
    flex: 0 0 150px;
}

.roadmap-panel {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.roadmap-panel h4 {
    color: #ffd700;
    font-size: 14px;
    margin-bottom: 8px;
    text-align: center;
    font-weight: bold;
}

/* 珠盘路 */
.bead-road {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 3px;
    height: 100%;
    padding: 5px;
}

.bead-cell {
    aspect-ratio: 1;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    min-width: 20px;
    min-height: 20px;
}

.bead-cell.banker {
    background: #ff6b6b;
    color: white;
}

.bead-cell.player {
    background: #4ecdc4;
    color: white;
}

.bead-cell.tie {
    background: #ffd700;
    color: black;
}

/* 大路 */
.big-road {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 2px;
    height: 100%;
    padding: 5px;
}

.big-road-cell {
    aspect-ratio: 1;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    min-width: 18px;
    min-height: 18px;
}

.big-road-cell.banker {
    background: #ff6b6b;
    color: white;
}

.big-road-cell.player {
    background: #4ecdc4;
    color: white;
}

/* 小路、大眼仔路、曱甴路 */
.small-road,
.big-eye-road,
.cockroach-road {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    gap: 1px;
    height: 100%;
    padding: 5px;
}

.derived-road-cell {
    aspect-ratio: 1;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 8px;
    min-height: 8px;
}

.derived-road-cell.red {
    background: #ff6b6b;
}

.derived-road-cell.blue {
    background: #4ecdc4;
}

/* 统计面板 */
.statistics-panel {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.statistics-panel h4 {
    color: #ffd700;
    font-size: 16px;
    margin-bottom: 10px;
    text-align: center;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    font-size: 14px;
}

.stat-item span:first-child {
    color: #cccccc;
}

.stat-item span:last-child {
    color: #ffd700;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .game-main {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .roadmap-container {
        flex-direction: column;
        height: auto;
    }

    .roadmap-left,
    .roadmap-center,
    .roadmap-right {
        flex: none;
    }

    .roadmap-left {
        order: 1;
    }

    .roadmap-center {
        order: 2;
    }

    .roadmap-right {
        order: 3;
    }

    .roadmap-bottom {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .roadmap-section {
        padding: 15px;
    }

    .roadmap-container {
        gap: 15px;
        height: auto;
    }

    .bead-road {
        grid-template-columns: repeat(4, 1fr);
    }

    .big-road {
        grid-template-columns: repeat(8, 1fr);
    }

    .small-road,
    .big-eye-road,
    .cockroach-road {
        grid-template-columns: repeat(10, 1fr);
    }
}

@media (max-width: 768px) {
    #game-container {
        padding: 10px;
    }

    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .game-status {
        flex-direction: column;
        gap: 10px;
    }

    .modal-content {
        padding: 20px;
        margin: 20px;
    }

    .game-footer {
        flex-wrap: wrap;
        gap: 10px;
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功/错误提示 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #4CAF50;
}

.notification.error {
    background: #f44336;
}

.notification.warning {
    background: #ff9800;
}

/* 禁用状态 */
.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

/* 高亮效果 */
.highlight {
    animation: highlight 0.5s ease-in-out;
}

@keyframes highlight {
    0% { background-color: rgba(255, 215, 0, 0.3); }
    100% { background-color: transparent; }
}

/* 游戏结果样式 */
.result-scores {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.score-item {
    text-align: center;
    padding: 10px;
}

.score-item span:first-child {
    display: block;
    color: #cccccc;
    font-size: 14px;
    margin-bottom: 5px;
}

.score-item span:last-child {
    display: block;
    color: #ffd700;
    font-size: 24px;
    font-weight: bold;
}

.payout-summary {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    margin: 10px 0;
}

.payout-summary.win {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.payout-summary.lose {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid #f44336;
}

/* 渐入渐出动画 */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* 规则内容样式 */
.rules-content {
    text-align: left;
    max-height: 400px;
    overflow-y: auto;
}

.rules-content h3 {
    color: #ffd700;
    font-size: 18px;
    margin: 20px 0 10px 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    padding-bottom: 5px;
}

.rules-content h3:first-child {
    margin-top: 0;
}

.rules-content ul {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.rules-content li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    line-height: 1.5;
}

.rules-content li:last-child {
    border-bottom: none;
}

.rules-content strong {
    color: #4ecdc4;
}
