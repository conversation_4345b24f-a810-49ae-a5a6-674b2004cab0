/**
 * 数据存储模块
 * 管理本地存储的数据，包括用户设置、游戏进度、历史记录等
 */

class DataStorage {
    constructor() {
        this.storageKey = 'baccarat_game_data';
        this.defaultData = {
            playerBalance: 10000,
            gameHistory: [],
            roadmapData: {
                beadRoad: [],
                bigRoad: [],
                smallRoad: [],
                bigEyeRoad: [],
                cockroachRoad: []
            },
            statistics: {
                bankerWins: 0,
                playerWins: 0,
                tieWins: 0,
                totalGames: 0,
                totalWinnings: 0,
                totalLosses: 0
            },
            settings: {
                soundEnabled: true,
                animationEnabled: true,
                autoNextRound: false,
                language: 'zh-CN'
            },
            achievements: [],
            lastPlayTime: null
        };
        
        this.data = this.loadData();
    }
    
    /**
     * 从本地存储加载数据
     * @returns {Object} 游戏数据
     */
    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const parsedData = JSON.parse(stored);
                // 合并默认数据和存储数据，确保新字段的兼容性
                return this.mergeWithDefaults(parsedData, this.defaultData);
            }
        } catch (error) {
            console.error('Error loading data from localStorage:', error);
        }
        return Utils.deepClone(this.defaultData);
    }
    
    /**
     * 保存数据到本地存储
     */
    saveData() {
        try {
            this.data.lastPlayTime = new Date().toISOString();
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
        } catch (error) {
            console.error('Error saving data to localStorage:', error);
            Utils.showNotification('保存数据失败', 'error');
        }
    }
    
    /**
     * 合并默认数据和存储数据
     * @param {Object} stored 存储的数据
     * @param {Object} defaults 默认数据
     * @returns {Object} 合并后的数据
     */
    mergeWithDefaults(stored, defaults) {
        const merged = Utils.deepClone(defaults);
        
        Object.keys(stored).forEach(key => {
            if (typeof defaults[key] === 'object' && defaults[key] !== null && !Array.isArray(defaults[key])) {
                merged[key] = this.mergeWithDefaults(stored[key] || {}, defaults[key]);
            } else {
                merged[key] = stored[key];
            }
        });
        
        return merged;
    }
    
    /**
     * 获取玩家余额
     * @returns {number} 玩家余额
     */
    getPlayerBalance() {
        return this.data.playerBalance;
    }
    
    /**
     * 设置玩家余额
     * @param {number} balance 新余额
     */
    setPlayerBalance(balance) {
        this.data.playerBalance = Math.max(0, balance);
        this.saveData();
    }
    
    /**
     * 更新玩家余额
     * @param {number} amount 变化金额（正数为增加，负数为减少）
     * @returns {number} 更新后的余额
     */
    updatePlayerBalance(amount) {
        const newBalance = this.data.playerBalance + amount;
        this.setPlayerBalance(newBalance);
        return this.data.playerBalance;
    }
    
    /**
     * 添加游戏历史记录
     * @param {Object} gameResult 游戏结果
     */
    addGameHistory(gameResult) {
        const historyEntry = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            bankerCards: gameResult.bankerCards,
            playerCards: gameResult.playerCards,
            bankerPoints: gameResult.bankerPoints,
            playerPoints: gameResult.playerPoints,
            winner: gameResult.winner,
            bets: gameResult.bets,
            payout: gameResult.payout,
            balanceChange: gameResult.balanceChange
        };
        
        this.data.gameHistory.unshift(historyEntry);
        
        // 限制历史记录数量，保留最近1000条
        if (this.data.gameHistory.length > 1000) {
            this.data.gameHistory = this.data.gameHistory.slice(0, 1000);
        }
        
        this.saveData();
    }
    
    /**
     * 获取游戏历史记录
     * @param {number} limit 限制数量
     * @returns {Array} 历史记录数组
     */
    getGameHistory(limit = 50) {
        return this.data.gameHistory.slice(0, limit);
    }
    
    /**
     * 更新路单数据
     * @param {string} roadType 路单类型
     * @param {Object} newEntry 新条目
     */
    updateRoadmap(roadType, newEntry) {
        if (!this.data.roadmapData[roadType]) {
            this.data.roadmapData[roadType] = [];
        }
        
        this.data.roadmapData[roadType].push(newEntry);
        
        // 限制路单数据长度
        const maxLength = roadType === 'beadRoad' ? 300 : 200;
        if (this.data.roadmapData[roadType].length > maxLength) {
            this.data.roadmapData[roadType] = this.data.roadmapData[roadType].slice(-maxLength);
        }
        
        this.saveData();
    }
    
    /**
     * 获取路单数据
     * @param {string} roadType 路单类型
     * @returns {Array} 路单数据
     */
    getRoadmapData(roadType) {
        return this.data.roadmapData[roadType] || [];
    }
    
    /**
     * 清空路单数据
     */
    clearRoadmapData() {
        this.data.roadmapData = {
            beadRoad: [],
            bigRoad: [],
            smallRoad: [],
            bigEyeRoad: [],
            cockroachRoad: []
        };
        this.saveData();
    }
    
    /**
     * 更新统计数据
     * @param {Object} gameResult 游戏结果
     */
    updateStatistics(gameResult) {
        const stats = this.data.statistics;
        
        stats.totalGames++;
        
        // 更新胜负统计
        switch (gameResult.winner) {
            case 'banker':
                stats.bankerWins++;
                break;
            case 'player':
                stats.playerWins++;
                break;
            case 'tie':
                stats.tieWins++;
                break;
        }
        
        // 更新盈亏统计
        if (gameResult.balanceChange > 0) {
            stats.totalWinnings += gameResult.balanceChange;
        } else if (gameResult.balanceChange < 0) {
            stats.totalLosses += Math.abs(gameResult.balanceChange);
        }
        
        this.saveData();
    }
    
    /**
     * 获取统计数据
     * @returns {Object} 统计数据
     */
    getStatistics() {
        return Utils.deepClone(this.data.statistics);
    }
    
    /**
     * 重置统计数据
     */
    resetStatistics() {
        this.data.statistics = Utils.deepClone(this.defaultData.statistics);
        this.saveData();
    }
    
    /**
     * 获取设置
     * @param {string} key 设置键名
     * @returns {*} 设置值
     */
    getSetting(key) {
        return this.data.settings[key];
    }
    
    /**
     * 设置配置
     * @param {string} key 设置键名
     * @param {*} value 设置值
     */
    setSetting(key, value) {
        this.data.settings[key] = value;
        this.saveData();
    }
    
    /**
     * 获取所有设置
     * @returns {Object} 设置对象
     */
    getAllSettings() {
        return Utils.deepClone(this.data.settings);
    }
    
    /**
     * 重置所有设置
     */
    resetSettings() {
        this.data.settings = Utils.deepClone(this.defaultData.settings);
        this.saveData();
    }
    
    /**
     * 添加成就
     * @param {string} achievementId 成就ID
     * @param {Object} achievementData 成就数据
     */
    addAchievement(achievementId, achievementData) {
        if (!this.data.achievements.find(a => a.id === achievementId)) {
            this.data.achievements.push({
                id: achievementId,
                timestamp: new Date().toISOString(),
                ...achievementData
            });
            this.saveData();
            return true;
        }
        return false;
    }
    
    /**
     * 获取成就列表
     * @returns {Array} 成就数组
     */
    getAchievements() {
        return Utils.deepClone(this.data.achievements);
    }
    
    /**
     * 导出数据
     * @returns {string} JSON格式的数据字符串
     */
    exportData() {
        return JSON.stringify(this.data, null, 2);
    }
    
    /**
     * 导入数据
     * @param {string} jsonData JSON格式的数据字符串
     * @returns {boolean} 导入是否成功
     */
    importData(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            this.data = this.mergeWithDefaults(importedData, this.defaultData);
            this.saveData();
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }
    
    /**
     * 重置所有数据
     */
    resetAllData() {
        this.data = Utils.deepClone(this.defaultData);
        this.saveData();
    }
    
    /**
     * 获取数据摘要（用于调试）
     * @returns {Object} 数据摘要
     */
    getDataSummary() {
        return {
            balance: this.data.playerBalance,
            totalGames: this.data.statistics.totalGames,
            historyCount: this.data.gameHistory.length,
            lastPlayTime: this.data.lastPlayTime,
            settings: this.data.settings
        };
    }
}

// 创建全局数据存储实例
window.dataStorage = new DataStorage();
