/**
 * 动画管理模块
 * 实现游戏中的各种动画效果，包括发牌动画、筹码动画、翻牌效果等
 */

class AnimationManager {
    constructor() {
        this.animationQueue = [];
        this.isAnimating = false;
        this.animationSettings = {
            cardDealDuration: 800,
            cardRevealDuration: 600,
            chipFlyDuration: 1000,
            countdownPulseDuration: 1000
        };
    }
    
    /**
     * 添加动画到队列
     * @param {Function} animationFunction 动画函数
     * @param {number} delay 延迟时间（毫秒）
     */
    addToQueue(animationFunction, delay = 0) {
        this.animationQueue.push({
            func: animationFunction,
            delay: delay
        });
        
        if (!this.isAnimating) {
            this.processQueue();
        }
    }
    
    /**
     * 处理动画队列
     */
    async processQueue() {
        if (this.animationQueue.length === 0) {
            this.isAnimating = false;
            return;
        }
        
        this.isAnimating = true;
        
        while (this.animationQueue.length > 0) {
            const animation = this.animationQueue.shift();
            
            if (animation.delay > 0) {
                await Utils.delay(animation.delay);
            }
            
            try {
                await animation.func();
            } catch (error) {
                console.error('Animation error:', error);
            }
        }
        
        this.isAnimating = false;
    }
    
    /**
     * 清空动画队列
     */
    clearQueue() {
        this.animationQueue = [];
        this.isAnimating = false;
    }
    
    /**
     * 筹码飞行动画
     * @param {HTMLElement} fromElement 起始元素
     * @param {HTMLElement} toElement 目标元素
     * @param {number} chipValue 筹码面值
     * @returns {Promise} 动画完成的Promise
     */
    animateChipFly(fromElement, toElement, chipValue) {
        return new Promise((resolve) => {
            const fromPos = Utils.getElementPosition(fromElement);
            const toPos = Utils.getElementPosition(toElement);
            
            // 创建飞行的筹码元素
            const flyingChip = Utils.createElement('div', {
                className: `chip chip-animation`,
                dataset: { value: chipValue }
            }, chipValue.toString());
            
            // 设置初始位置
            Object.assign(flyingChip.style, {
                position: 'fixed',
                left: `${fromPos.x + fromPos.width / 2 - 30}px`,
                top: `${fromPos.y + fromPos.height / 2 - 30}px`,
                width: '60px',
                height: '60px',
                zIndex: '1000',
                pointerEvents: 'none'
            });
            
            document.body.appendChild(flyingChip);
            
            // 添加飞行动画类
            Utils.addClass(flyingChip, 'chip-flying');
            
            // 计算目标位置
            const targetX = toPos.x + toPos.width / 2 - 30;
            const targetY = toPos.y + toPos.height / 2 - 30;
            
            // 执行动画
            flyingChip.animate([
                {
                    transform: 'translate(0, 0) scale(1) rotate(0deg)',
                    opacity: 1
                },
                {
                    transform: `translate(${targetX - fromPos.x - fromPos.width / 2 + 30}px, ${targetY - fromPos.y - fromPos.height / 2 + 30}px) scale(0.8) rotate(360deg)`,
                    opacity: 1
                }
            ], {
                duration: this.animationSettings.chipFlyDuration,
                easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            }).onfinish = () => {
                // 移除飞行的筹码
                if (flyingChip.parentNode) {
                    flyingChip.parentNode.removeChild(flyingChip);
                }
                
                // 在目标位置添加堆叠效果
                Utils.addClass(toElement, 'chip-stack');
                setTimeout(() => {
                    Utils.removeClass(toElement, 'chip-stack');
                }, 300);
                
                resolve();
            };
        });
    }
    
    /**
     * 发牌动画
     * @param {HTMLElement} cardElement 卡牌元素
     * @param {HTMLElement} targetSlot 目标卡槽
     * @returns {Promise} 动画完成的Promise
     */
    animateCardDeal(cardElement, targetSlot) {
        return new Promise((resolve) => {
            const targetPos = Utils.getElementPosition(targetSlot);
            
            // 设置卡牌初始位置（牌堆位置）
            Object.assign(cardElement.style, {
                position: 'fixed',
                left: '50px',
                top: '50px',
                zIndex: '100',
                transform: 'rotate(-30deg) scale(0.8)',
                opacity: '0'
            });
            
            // 添加到目标卡槽
            targetSlot.appendChild(cardElement);
            
            // 添加发牌动画类
            Utils.addClass(cardElement, 'card-dealing');
            
            // 执行动画
            cardElement.animate([
                {
                    transform: 'translate(-300px, -150px) rotate(-30deg) scale(0.8)',
                    opacity: 0
                },
                {
                    transform: 'translate(20px, 10px) rotate(5deg) scale(1.1)',
                    opacity: 1,
                    offset: 0.7
                },
                {
                    transform: 'translate(0, 0) rotate(0deg) scale(1)',
                    opacity: 1
                }
            ], {
                duration: this.animationSettings.cardDealDuration,
                easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            }).onfinish = () => {
                // 重置样式
                Object.assign(cardElement.style, {
                    position: 'relative',
                    left: 'auto',
                    top: 'auto',
                    transform: 'none',
                    opacity: '1',
                    zIndex: 'auto'
                });
                
                Utils.removeClass(cardElement, 'card-dealing');
                Utils.addClass(targetSlot, 'occupied');
                
                resolve();
            };
        });
    }
    
    /**
     * 翻牌动画
     * @param {HTMLElement} cardElement 卡牌元素
     * @returns {Promise} 动画完成的Promise
     */
    animateCardReveal(cardElement) {
        return new Promise((resolve) => {
            Utils.addClass(cardElement, 'card-reveal');
            
            cardElement.animate([
                { transform: 'rotateY(0deg) scale(1)' },
                { transform: 'rotateY(90deg) scale(1.1)', offset: 0.5 },
                { transform: 'rotateY(180deg) scale(1)' }
            ], {
                duration: this.animationSettings.cardRevealDuration,
                easing: 'ease-in-out'
            }).onfinish = () => {
                Utils.removeClass(cardElement, 'card-reveal');
                Utils.addClass(cardElement, 'flipped');
                resolve();
            };
        });
    }
    
    /**
     * 胜利动画
     * @param {HTMLElement} element 要添加动画的元素
     * @returns {Promise} 动画完成的Promise
     */
    animateVictory(element) {
        return new Promise((resolve) => {
            Utils.addClass(element, 'victory-animation');
            
            setTimeout(() => {
                Utils.removeClass(element, 'victory-animation');
                resolve();
            }, 2000);
        });
    }
    
    /**
     * 失败动画
     * @param {HTMLElement} element 要添加动画的元素
     * @returns {Promise} 动画完成的Promise
     */
    animateDefeat(element) {
        return new Promise((resolve) => {
            Utils.addClass(element, 'defeat-animation');
            
            setTimeout(() => {
                Utils.removeClass(element, 'defeat-animation');
                resolve();
            }, 1500);
        });
    }
    
    /**
     * 金币飞舞动画
     * @param {HTMLElement} centerElement 中心元素
     * @param {number} coinCount 金币数量
     * @returns {Promise} 动画完成的Promise
     */
    animateCoinBurst(centerElement, coinCount = 10) {
        return new Promise((resolve) => {
            const centerPos = Utils.getElementPosition(centerElement);
            const centerX = centerPos.x + centerPos.width / 2;
            const centerY = centerPos.y + centerPos.height / 2;
            
            const coins = [];
            
            for (let i = 0; i < coinCount; i++) {
                const coin = Utils.createElement('div', {
                    className: 'coin-animation'
                });
                
                // 随机方向和距离
                const angle = (360 / coinCount) * i + Utils.randomInt(-30, 30);
                const distance = Utils.randomInt(50, 150);
                const dx = Math.cos(angle * Math.PI / 180) * distance;
                const dy = Math.sin(angle * Math.PI / 180) * distance;
                
                Object.assign(coin.style, {
                    position: 'fixed',
                    left: `${centerX - 15}px`,
                    top: `${centerY - 15}px`,
                    '--dx': `${dx}px`,
                    '--dy': `${dy}px`
                });
                
                document.body.appendChild(coin);
                coins.push(coin);
                
                // 添加动画类
                Utils.addClass(coin, 'coin-fly');
            }
            
            // 2秒后清理
            setTimeout(() => {
                coins.forEach(coin => {
                    if (coin.parentNode) {
                        coin.parentNode.removeChild(coin);
                    }
                });
                resolve();
            }, 2000);
        });
    }
    
    /**
     * 粒子爆发动画
     * @param {HTMLElement} centerElement 中心元素
     * @param {number} particleCount 粒子数量
     * @returns {Promise} 动画完成的Promise
     */
    animateParticleBurst(centerElement, particleCount = 20) {
        return new Promise((resolve) => {
            const centerPos = Utils.getElementPosition(centerElement);
            const centerX = centerPos.x + centerPos.width / 2;
            const centerY = centerPos.y + centerPos.height / 2;
            
            const particles = [];
            
            for (let i = 0; i < particleCount; i++) {
                const particle = Utils.createElement('div', {
                    className: 'particle'
                });
                
                // 随机方向和距离
                const angle = Utils.randomInt(0, 360);
                const distance = Utils.randomInt(30, 100);
                const dx = Math.cos(angle * Math.PI / 180) * distance;
                const dy = Math.sin(angle * Math.PI / 180) * distance;
                
                Object.assign(particle.style, {
                    position: 'fixed',
                    left: `${centerX - 2}px`,
                    top: `${centerY - 2}px`,
                    '--dx': `${dx}px`,
                    '--dy': `${dy}px`
                });
                
                document.body.appendChild(particle);
                particles.push(particle);
                
                // 添加动画类
                Utils.addClass(particle, 'particle-burst');
            }
            
            // 1.5秒后清理
            setTimeout(() => {
                particles.forEach(particle => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                });
                resolve();
            }, 1500);
        });
    }
    
    /**
     * 余额变化动画
     * @param {HTMLElement} balanceElement 余额元素
     * @param {boolean} isIncrease 是否为增加
     * @returns {Promise} 动画完成的Promise
     */
    animateBalanceChange(balanceElement, isIncrease) {
        return new Promise((resolve) => {
            const animationClass = isIncrease ? 'balance-increase' : 'balance-decrease';
            Utils.addClass(balanceElement, animationClass);
            
            setTimeout(() => {
                Utils.removeClass(balanceElement, animationClass);
                resolve();
            }, 1000);
        });
    }
    
    /**
     * 倒计时紧急动画
     * @param {HTMLElement} timerElement 计时器元素
     */
    startCountdownUrgentAnimation(timerElement) {
        Utils.addClass(timerElement, 'countdown-urgent');
    }
    
    /**
     * 停止倒计时紧急动画
     * @param {HTMLElement} timerElement 计时器元素
     */
    stopCountdownUrgentAnimation(timerElement) {
        Utils.removeClass(timerElement, 'countdown-urgent');
    }
    
    /**
     * 按钮点击动画
     * @param {HTMLElement} buttonElement 按钮元素
     * @returns {Promise} 动画完成的Promise
     */
    animateButtonClick(buttonElement) {
        return new Promise((resolve) => {
            Utils.addClass(buttonElement, 'button-click');
            
            setTimeout(() => {
                Utils.removeClass(buttonElement, 'button-click');
                resolve();
            }, 200);
        });
    }
    
    /**
     * 路单更新动画
     * @param {HTMLElement} roadmapCell 路单格子元素
     * @returns {Promise} 动画完成的Promise
     */
    animateRoadmapUpdate(roadmapCell) {
        return new Promise((resolve) => {
            Utils.addClass(roadmapCell, 'roadmap-update');
            
            setTimeout(() => {
                Utils.removeClass(roadmapCell, 'roadmap-update');
                resolve();
            }, 500);
        });
    }
}

// 创建全局动画管理实例
window.animationManager = new AnimationManager();
