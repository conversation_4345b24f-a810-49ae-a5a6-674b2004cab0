/* 扑克牌样式 */
.card {
    width: 90px;
    height: 126px;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transform-style: preserve-3d;
    transition: transform 0.6s;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.card.flipped {
    transform: rotateY(180deg);
}

.card-face,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card-back {
    background-image: url('../PNG/blue_back.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card-back::before,
.card-back::after {
    display: none;
}

.card-face {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transform: rotateY(180deg);
    border: 1px solid rgba(0, 0, 0, 0.2);
}

/* 隐藏文字元素，使用PNG图片 */
.card-rank,
.card-suit {
    display: none;
}

/* 隐藏所有文字相关样式 */
.card-face::before,
.card-face::after {
    display: none;
}

/* 动画效果 */
.card.dealing {
    animation: dealCard 0.8s ease-out;
}

@keyframes dealCard {
    0% {
        transform: translateX(-200px) translateY(-100px) rotate(-45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(0) translateY(0) rotate(0deg);
        opacity: 1;
    }
}

.card.flipping {
    animation: flipCard 0.6s ease-in-out;
}

@keyframes flipCard {
    0% {
        transform: rotateY(0deg) scale(1);
    }
    50% {
        transform: rotateY(90deg) scale(1.1);
    }
    100% {
        transform: rotateY(180deg) scale(1);
    }
}

/* 牌堆样式 */
.deck {
    position: relative;
    width: 90px;
    height: 126px;
}

.deck .card {
    position: absolute;
    top: 0;
    left: 0;
}

.deck .card:nth-child(1) { transform: translateY(-1px) translateX(-1px); }
.deck .card:nth-child(2) { transform: translateY(-2px) translateX(-2px); }
.deck .card:nth-child(3) { transform: translateY(-3px) translateX(-3px); }

/* 高亮效果 */
.card.highlight {
    animation: cardHighlight 1s ease-in-out;
    z-index: 10;
}

@keyframes cardHighlight {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
    }
}

/* 牌面点数显示 */
.card-points {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #ffd700;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .card-points {
    opacity: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .card {
        width: 70px;
        height: 98px;
    }

    .card-rank {
        font-size: 12px;
    }

    .card-suit {
        font-size: 16px;
    }

    .card-face::before,
    .card-face::after {
        font-size: 6px;
    }

    .card-back::after {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .card {
        width: 60px;
        height: 84px;
    }

    .card-rank {
        font-size: 10px;
    }

    .card-suit {
        font-size: 14px;
    }

    .card-face::before,
    .card-face::after {
        font-size: 5px;
    }

    .card-back::after {
        font-size: 10px;
    }
}

/* 特效增强 */
.card.winning {
    animation: winningCard 2s ease-in-out infinite;
}

@keyframes winningCard {
    0%, 100% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.05);
        filter: brightness(1.2);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
}

.card.losing {
    animation: losingCard 1s ease-out;
    opacity: 0.6;
}

@keyframes losingCard {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    100% {
        transform: scale(0.95);
        filter: brightness(0.7) grayscale(0.3);
    }
}

/* 3D效果增强 */
.card-3d {
    perspective: 1000px;
}

.card-3d .card {
    transform-style: preserve-3d;
}

.card-3d .card:hover {
    transform: rotateX(10deg) rotateY(10deg);
}

/* 发光效果 */
.card.glow {
    box-shadow:
        0 0 10px rgba(255, 215, 0, 0.5),
        0 0 20px rgba(255, 215, 0, 0.3),
        0 0 30px rgba(255, 215, 0, 0.1);
}
