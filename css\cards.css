/* 扑克牌样式 */
.card {
    width: 80px;
    height: 112px;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transform-style: preserve-3d;
    transition: transform 0.6s;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.card.flipped {
    transform: rotateY(180deg);
}

.card-face,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card-back {
    background: linear-gradient(135deg, #1a237e, #3949ab);
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    border: 2px solid #ffd700;
}

.card-back::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    right: 10%;
    bottom: 10%;
    border: 2px solid rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

.card-back::after {
    content: '♠♥♦♣';
    font-size: 16px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    font-weight: bold;
}

.card-face {
    background: #ffffff;
    color: #000000;
    transform: rotateY(180deg);
    flex-direction: column;
    font-weight: bold;
    border: 1px solid #cccccc;
}

/* 花色颜色 */
.card-face.red {
    color: #d32f2f;
}

.card-face.black {
    color: #000000;
}

/* 牌面数字和花色 */
.card-rank {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 2px;
}

.card-suit {
    font-size: 20px;
    line-height: 1;
}

/* 特殊牌面样式 */
.card-face.ace .card-rank {
    font-size: 16px;
}

.card-face.face-card .card-rank {
    font-size: 12px;
}

.card-face.face-card .card-suit {
    font-size: 16px;
}

/* 牌面角标 */
.card-face::before {
    content: attr(data-rank) ' ' attr(data-suit);
    position: absolute;
    top: 4px;
    left: 4px;
    font-size: 8px;
    line-height: 1;
}

.card-face::after {
    content: attr(data-rank) ' ' attr(data-suit);
    position: absolute;
    bottom: 4px;
    right: 4px;
    font-size: 8px;
    line-height: 1;
    transform: rotate(180deg);
}

/* 动画效果 */
.card.dealing {
    animation: dealCard 0.8s ease-out;
}

@keyframes dealCard {
    0% {
        transform: translateX(-200px) translateY(-100px) rotate(-45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(0) translateY(0) rotate(0deg);
        opacity: 1;
    }
}

.card.flipping {
    animation: flipCard 0.6s ease-in-out;
}

@keyframes flipCard {
    0% {
        transform: rotateY(0deg) scale(1);
    }
    50% {
        transform: rotateY(90deg) scale(1.1);
    }
    100% {
        transform: rotateY(180deg) scale(1);
    }
}

/* 牌堆样式 */
.deck {
    position: relative;
    width: 80px;
    height: 112px;
}

.deck .card {
    position: absolute;
    top: 0;
    left: 0;
}

.deck .card:nth-child(1) { transform: translateY(-1px) translateX(-1px); }
.deck .card:nth-child(2) { transform: translateY(-2px) translateX(-2px); }
.deck .card:nth-child(3) { transform: translateY(-3px) translateX(-3px); }

/* 高亮效果 */
.card.highlight {
    animation: cardHighlight 1s ease-in-out;
    z-index: 10;
}

@keyframes cardHighlight {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
    }
}

/* 牌面点数显示 */
.card-points {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #ffd700;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .card-points {
    opacity: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .card {
        width: 60px;
        height: 84px;
    }
    
    .card-rank {
        font-size: 12px;
    }
    
    .card-suit {
        font-size: 16px;
    }
    
    .card-face::before,
    .card-face::after {
        font-size: 6px;
    }
    
    .card-back::after {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .card {
        width: 50px;
        height: 70px;
    }
    
    .card-rank {
        font-size: 10px;
    }
    
    .card-suit {
        font-size: 14px;
    }
    
    .card-face::before,
    .card-face::after {
        font-size: 5px;
    }
    
    .card-back::after {
        font-size: 10px;
    }
}

/* 特效增强 */
.card.winning {
    animation: winningCard 2s ease-in-out infinite;
}

@keyframes winningCard {
    0%, 100% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.05);
        filter: brightness(1.2);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
}

.card.losing {
    animation: losingCard 1s ease-out;
    opacity: 0.6;
}

@keyframes losingCard {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    100% {
        transform: scale(0.95);
        filter: brightness(0.7) grayscale(0.3);
    }
}

/* 3D效果增强 */
.card-3d {
    perspective: 1000px;
}

.card-3d .card {
    transform-style: preserve-3d;
}

.card-3d .card:hover {
    transform: rotateX(10deg) rotateY(10deg);
}

/* 发光效果 */
.card.glow {
    box-shadow: 
        0 0 10px rgba(255, 215, 0, 0.5),
        0 0 20px rgba(255, 215, 0, 0.3),
        0 0 30px rgba(255, 215, 0, 0.1);
}
