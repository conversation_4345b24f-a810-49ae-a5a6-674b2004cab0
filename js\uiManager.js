/**
 * 用户界面管理模块
 * 负责渲染牌桌、处理用户交互、显示游戏信息等
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.roadmapManager = null;
        this.initializeElements();
        this.bindEvents();
        this.initializeRoadmap();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            // 游戏状态元素
            balance: document.getElementById('player-balance'),
            gamePhase: document.getElementById('game-phase'),
            countdownTimer: document.getElementById('countdown-timer'),

            // 卡牌区域
            bankerCards: document.getElementById('banker-cards'),
            playerCards: document.getElementById('player-cards'),
            bankerPoints: document.getElementById('banker-points'),
            playerPoints: document.getElementById('player-points'),

            // 下注区域
            betZones: document.querySelectorAll('.bet-zone'),
            betAmounts: {
                banker: document.getElementById('banker-bet'),
                player: document.getElementById('player-bet'),
                tie: document.getElementById('tie-bet'),
                'banker-pair': document.getElementById('banker-pair-bet'),
                'player-pair': document.getElementById('player-pair-bet'),
                lucky6: document.getElementById('lucky6-bet'),
                lucky7: document.getElementById('lucky7-bet')
            },

            // 筹码选择
            chips: document.querySelectorAll('.chip'),
            clearBetsBtn: document.getElementById('clear-bets'),

            // 路单区域
            roadmaps: {
                beadRoad: document.getElementById('bead-road'),
                bigRoad: document.getElementById('big-road'),
                smallRoad: document.getElementById('small-road'),
                bigEyeRoad: document.getElementById('big-eye-road'),
                cockroachRoad: document.getElementById('cockroach-road')
            },

            // 统计信息
            statistics: {
                bankerWins: document.getElementById('banker-wins'),
                playerWins: document.getElementById('player-wins'),
                tieWins: document.getElementById('tie-wins')
            },

            // 模态框
            resultModal: document.getElementById('result-modal'),
            resultTitle: document.getElementById('result-title'),
            resultDetails: document.getElementById('result-details'),
            payoutInfo: document.getElementById('payout-info'),
            nextRoundBtn: document.getElementById('next-round'),

            rulesModal: document.getElementById('rules-modal'),
            rulesBtn: document.getElementById('rules-btn'),
            closeRulesBtn: document.getElementById('close-rules'),

            // 底部按钮
            historyBtn: document.getElementById('history-btn'),
            settingsBtn: document.getElementById('settings-btn'),
            resetBalanceBtn: document.getElementById('reset-balance-btn'),
            addBalanceBtn: document.getElementById('add-balance-btn'),
            testCardsBtn: document.getElementById('test-cards-btn')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 筹码选择事件
        this.elements.chips.forEach(chip => {
            chip.addEventListener('click', (e) => {
                const chipValue = parseInt(e.target.dataset.value);
                this.selectChip(chipValue);
            });
        });

        // 下注区域点击事件
        this.elements.betZones.forEach(zone => {
            zone.addEventListener('click', (e) => {
                const betType = e.currentTarget.dataset.bet;
                this.placeBet(betType);
            });
        });

        // 清除下注按钮
        this.elements.clearBetsBtn.addEventListener('click', () => {
            this.clearAllBets();
        });

        // 下一局按钮
        this.elements.nextRoundBtn.addEventListener('click', () => {
            this.startNextRound();
        });

        // 游戏规则按钮
        this.elements.rulesBtn.addEventListener('click', () => {
            this.showRulesModal();
        });

        this.elements.closeRulesBtn.addEventListener('click', () => {
            this.hideRulesModal();
        });

        // 历史记录按钮
        this.elements.historyBtn.addEventListener('click', () => {
            this.showHistoryModal();
        });

        // 设置按钮
        this.elements.settingsBtn.addEventListener('click', () => {
            this.showSettingsModal();
        });

        // 重置余额按钮
        this.elements.resetBalanceBtn.addEventListener('click', () => {
            this.resetBalance();
        });

        // 增加余额按钮
        this.elements.addBalanceBtn.addEventListener('click', () => {
            this.addBalance();
        });

        // 测试卡牌按钮
        this.elements.testCardsBtn.addEventListener('click', () => {
            this.testCardImages();
        });

        // 模态框背景点击关闭
        [this.elements.resultModal, this.elements.rulesModal].forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal);
                }
            });
        });

        // 状态管理器事件监听
        this.bindStateEvents();
    }

    /**
     * 绑定状态管理器事件
     */
    bindStateEvents() {
        stateManager.on('roundStarted', (data) => {
            this.updateGamePhase('等待下注');
            this.updateCountdown(data.countdown || GAME_CONSTANTS.BETTING_TIME);
            this.clearCardAreas();
            this.updateBetDisplay();
        });

        stateManager.on('countdownUpdate', (data) => {
            this.updateCountdown(data.countdown);
        });

        stateManager.on('dealingStarted', (data) => {
            this.updateGamePhase('发牌中');
            this.disableBetting();
        });

        stateManager.on('revealingStarted', (data) => {
            this.updateGamePhase('揭牌中');
        });

        stateManager.on('gameEnded', (data) => {
            this.updateGamePhase('游戏结束');
            this.showGameResult(data);
            this.updateStatistics();
        });

        stateManager.on('betPlaced', (data) => {
            this.updateBetDisplay();
            this.updateBalance();
        });

        stateManager.on('betsCleared', (data) => {
            this.updateBetDisplay();
        });

        stateManager.on('balanceUpdated', (data) => {
            this.updateBalance();
            if (data.change !== 0) {
                animationManager.animateBalanceChange(this.elements.balance, data.change > 0);
            }
        });

        stateManager.on('chipSelected', (data) => {
            this.updateChipSelection(data.chipValue);
        });
    }

    /**
     * 初始化路单管理器
     */
    initializeRoadmap() {
        this.roadmapManager = new RoadmapManager(this.elements.roadmaps);
    }

    /**
     * 更新余额显示
     */
    updateBalance() {
        const balance = stateManager.getPlayerState().balance;
        this.elements.balance.textContent = Utils.formatCurrency(balance);
    }

    /**
     * 更新游戏阶段显示
     * @param {string} phase 游戏阶段
     */
    updateGamePhase(phase) {
        this.elements.gamePhase.textContent = phase;
    }

    /**
     * 更新倒计时显示
     * @param {number} countdown 倒计时秒数
     */
    updateCountdown(countdown) {
        this.elements.countdownTimer.textContent = countdown;

        // 倒计时小于等于5秒时添加紧急动画
        if (countdown <= 5 && countdown > 0) {
            animationManager.startCountdownUrgentAnimation(this.elements.countdownTimer);
        } else {
            animationManager.stopCountdownUrgentAnimation(this.elements.countdownTimer);
        }
    }

    /**
     * 选择筹码
     * @param {number} chipValue 筹码面值
     */
    selectChip(chipValue) {
        stateManager.setSelectedChip(chipValue);
        animationManager.animateButtonClick(event.target);
    }

    /**
     * 更新筹码选择显示
     * @param {number} selectedValue 选中的筹码面值
     */
    updateChipSelection(selectedValue) {
        this.elements.chips.forEach(chip => {
            const chipValue = parseInt(chip.dataset.value);
            if (chipValue === selectedValue) {
                Utils.addClass(chip, 'selected');
            } else {
                Utils.removeClass(chip, 'selected');
            }
        });
    }

    /**
     * 下注
     * @param {string} betType 下注类型
     */
    placeBet(betType) {
        const selectedChip = stateManager.getPlayerState().selectedChip;
        const success = stateManager.placeBet(betType, selectedChip);

        if (success) {
            // 播放筹码飞行动画
            const chipElement = document.querySelector('.chip.selected');
            const betZone = document.querySelector(`[data-bet="${betType}"]`);

            if (chipElement && betZone) {
                animationManager.animateChipFly(chipElement, betZone, selectedChip);
            }
        }
    }

    /**
     * 清除所有下注
     */
    clearAllBets() {
        const success = stateManager.clearAllBets();
        if (success) {
            animationManager.animateButtonClick(this.elements.clearBetsBtn);
        }
    }

    /**
     * 更新下注显示
     */
    updateBetDisplay() {
        const currentBets = stateManager.getPlayerState().currentBets;

        // 更新下注金额显示
        Object.keys(this.elements.betAmounts).forEach(betType => {
            const amount = currentBets[betType] || 0;
            this.elements.betAmounts[betType].textContent = Utils.formatCurrency(amount);
        });

        // 更新下注区域样式
        this.elements.betZones.forEach(zone => {
            const betType = zone.dataset.bet;
            const hasBet = currentBets[betType] && currentBets[betType] > 0;

            if (hasBet) {
                Utils.addClass(zone, 'has-bet');
            } else {
                Utils.removeClass(zone, 'has-bet');
            }
        });
    }

    /**
     * 禁用下注
     */
    disableBetting() {
        this.elements.betZones.forEach(zone => {
            Utils.addClass(zone, 'disabled');
        });
        Utils.addClass(this.elements.clearBetsBtn, 'disabled');
    }

    /**
     * 启用下注
     */
    enableBetting() {
        this.elements.betZones.forEach(zone => {
            Utils.removeClass(zone, 'disabled');
        });
        Utils.removeClass(this.elements.clearBetsBtn, 'disabled');
    }

    /**
     * 清空卡牌区域
     */
    clearCardAreas() {
        [this.elements.bankerCards, this.elements.playerCards].forEach(area => {
            const slots = area.querySelectorAll('.card-slot');
            slots.forEach(slot => {
                slot.innerHTML = '';
                Utils.removeClass(slot, 'occupied');
            });
        });

        this.elements.bankerPoints.textContent = '0';
        this.elements.playerPoints.textContent = '0';
    }

    /**
     * 创建卡牌元素
     * @param {Object} card 卡牌数据
     * @param {boolean} faceDown 是否背面朝上
     * @returns {HTMLElement} 卡牌元素
     */
    createCardElement(card, faceDown = true) {
        const cardElement = Utils.createElement('div', {
            className: 'card'
        });

        // 卡牌背面
        const cardBack = Utils.createElement('div', {
            className: 'card-back'
        });

        // 卡牌正面
        const displayInfo = gameLogic.getCardDisplayInfo(card);
        const cardFace = Utils.createElement('div', {
            className: 'card-face',
            style: {
                backgroundImage: `url('${displayInfo.imagePath}')`
            },
            dataset: {
                rank: displayInfo.rank,
                suit: displayInfo.suit
            }
        });

        // 保留文字元素以备调试使用，但CSS中已隐藏
        const rankElement = Utils.createElement('div', {
            className: 'card-rank'
        }, displayInfo.rank);

        const suitElement = Utils.createElement('div', {
            className: 'card-suit'
        }, displayInfo.suit);

        cardFace.appendChild(rankElement);
        cardFace.appendChild(suitElement);

        cardElement.appendChild(cardBack);
        cardElement.appendChild(cardFace);

        if (!faceDown) {
            Utils.addClass(cardElement, 'flipped');
        }

        return cardElement;
    }

    /**
     * 发牌到指定位置
     * @param {Object} card 卡牌数据
     * @param {string} position 位置 ('banker' 或 'player')
     * @param {number} cardIndex 卡牌索引
     * @returns {Promise} 动画完成的Promise
     */
    async dealCardTo(card, position, cardIndex) {
        const cardElement = this.createCardElement(card, true);
        const targetArea = position === 'banker' ? this.elements.bankerCards : this.elements.playerCards;
        const targetSlot = targetArea.children[cardIndex];

        if (targetSlot) {
            await animationManager.animateCardDeal(cardElement, targetSlot);
        }

        return cardElement;
    }

    /**
     * 翻牌
     * @param {HTMLElement} cardElement 卡牌元素
     * @returns {Promise} 动画完成的Promise
     */
    async revealCard(cardElement) {
        return animationManager.animateCardReveal(cardElement);
    }

    /**
     * 更新点数显示
     * @param {string} position 位置 ('banker' 或 'player')
     * @param {number} points 点数
     */
    updatePoints(position, points) {
        const element = position === 'banker' ? this.elements.bankerPoints : this.elements.playerPoints;
        element.textContent = points.toString();

        // 添加高亮效果
        Utils.addClass(element, 'highlight');
        setTimeout(() => {
            Utils.removeClass(element, 'highlight');
        }, 500);
    }

    /**
     * 显示游戏结果
     * @param {Object} resultData 结果数据
     */
    showGameResult(resultData) {
        const { result, payout, balanceChange } = resultData;

        // 设置结果标题
        let title = '';
        switch (result.winner) {
            case 'banker':
                title = '庄家获胜';
                break;
            case 'player':
                title = '闲家获胜';
                break;
            case 'tie':
                title = '和局';
                break;
        }
        this.elements.resultTitle.textContent = title;

        // 设置结果详情
        const details = `
            <div class="result-scores">
                <div class="score-item">
                    <span>庄家：</span>
                    <span>${result.bankerPoints}点</span>
                </div>
                <div class="score-item">
                    <span>闲家：</span>
                    <span>${result.playerPoints}点</span>
                </div>
            </div>
        `;
        this.elements.resultDetails.innerHTML = details;

        // 设置派彩信息
        const payoutText = balanceChange >= 0 ?
            `恭喜！您赢得了 ${Utils.formatCurrency(balanceChange)}` :
            `很遗憾，您输了 ${Utils.formatCurrency(Math.abs(balanceChange))}`;

        this.elements.payoutInfo.innerHTML = `
            <div class="payout-summary ${balanceChange >= 0 ? 'win' : 'lose'}">
                ${payoutText}
            </div>
        `;

        // 显示模态框
        this.showModal(this.elements.resultModal);

        // 更新路单
        this.roadmapManager.addResult(result);
    }

    /**
     * 开始下一轮
     */
    startNextRound() {
        this.hideModal(this.elements.resultModal);
        this.enableBetting();

        if (stateManager.canStartNewGame()) {
            // 这里会由主控制器处理
            window.dispatchEvent(new CustomEvent('startNewRound'));
        } else {
            Utils.showNotification('余额不足，无法开始新游戏', 'warning');
        }
    }

    /**
     * 更新统计信息
     */
    updateStatistics() {
        const stats = dataStorage.getStatistics();
        this.elements.statistics.bankerWins.textContent = stats.bankerWins;
        this.elements.statistics.playerWins.textContent = stats.playerWins;
        this.elements.statistics.tieWins.textContent = stats.tieWins;
    }

    /**
     * 显示模态框
     * @param {HTMLElement} modal 模态框元素
     */
    showModal(modal) {
        Utils.removeClass(modal, 'hidden');
        Utils.addClass(modal, 'fade-in');
    }

    /**
     * 隐藏模态框
     * @param {HTMLElement} modal 模态框元素
     */
    hideModal(modal) {
        Utils.addClass(modal, 'fade-out');
        setTimeout(() => {
            Utils.addClass(modal, 'hidden');
            Utils.removeClass(modal, 'fade-in');
            Utils.removeClass(modal, 'fade-out');
        }, 500);
    }

    /**
     * 显示游戏规则模态框
     */
    showRulesModal() {
        // 这里可以动态加载规则内容
        this.showModal(this.elements.rulesModal);
    }

    /**
     * 隐藏游戏规则模态框
     */
    hideRulesModal() {
        this.hideModal(this.elements.rulesModal);
    }

    /**
     * 显示历史记录模态框
     */
    showHistoryModal() {
        // 这里可以实现历史记录显示
        Utils.showNotification('历史记录功能开发中', 'info');
    }

    /**
     * 显示设置模态框
     */
    showSettingsModal() {
        // 这里可以实现设置功能
        Utils.showNotification('设置功能开发中', 'info');
    }

    /**
     * 重置余额
     */
    resetBalance() {
        if (confirm('确定要重置余额到10000吗？')) {
            dataStorage.setPlayerBalance(10000);
            stateManager.refreshBalance(); // 刷新状态管理器中的余额
            this.updateBalance();
            Utils.showNotification('余额已重置为10000', 'success');

            // 如果当前没有游戏进行，重新启用下注
            if (!stateManager.getGameState().isGameActive) {
                this.enableBetting();
            }
        }
    }

    /**
     * 增加余额
     */
    addBalance() {
        const amount = prompt('请输入要增加的金额：', '5000');
        if (amount && !isNaN(amount)) {
            const addAmount = parseInt(amount);
            if (addAmount > 0) {
                dataStorage.updatePlayerBalance(addAmount);
                stateManager.refreshBalance(); // 刷新状态管理器中的余额
                this.updateBalance();
                Utils.showNotification(`成功增加${Utils.formatCurrency(addAmount)}余额`, 'success');

                // 如果当前没有游戏进行，重新启用下注
                if (!stateManager.getGameState().isGameActive) {
                    this.enableBetting();
                }
            } else {
                Utils.showNotification('请输入有效的正数金额', 'warning');
            }
        }
    }

    /**
     * 测试卡牌图片
     */
    testCardImages() {
        // 清空卡牌区域
        this.clearCardAreas();

        // 创建测试卡牌
        const testCards = [
            { rank: 1, suit: 'hearts' },    // 红心A
            { rank: 13, suit: 'spades' },   // 黑桃K
            { rank: 12, suit: 'diamonds' }, // 方块Q
            { rank: 11, suit: 'clubs' }     // 梅花J
        ];

        // 在庄家区域显示测试卡牌
        testCards.forEach((cardData, index) => {
            if (index < 2) {
                const cardElement = this.createCardElement(cardData, false); // 正面朝上
                const targetSlot = this.elements.bankerCards.children[index];
                if (targetSlot) {
                    targetSlot.appendChild(cardElement);
                    Utils.addClass(targetSlot, 'occupied');
                }
            }
        });

        // 在闲家区域显示测试卡牌
        testCards.forEach((cardData, index) => {
            if (index >= 2) {
                const cardElement = this.createCardElement(cardData, false); // 正面朝上
                const targetSlot = this.elements.playerCards.children[index - 2];
                if (targetSlot) {
                    targetSlot.appendChild(cardElement);
                    Utils.addClass(targetSlot, 'occupied');
                }
            }
        });

        Utils.showNotification('测试卡牌已显示，检查PNG图片是否正确加载', 'info');
    }

    /**
     * 初始化UI
     */
    initialize() {
        this.updateBalance();
        this.updateBetDisplay();
        this.updateStatistics();
        this.updateChipSelection(100); // 默认选中100筹码
    }
}

// 路单管理器类
class RoadmapManager {
    constructor(roadmapElements) {
        this.elements = roadmapElements;
        this.data = {
            beadRoad: [],
            bigRoad: [],
            derivedRoads: []
        };
        this.loadExistingData();
    }

    /**
     * 加载现有路单数据
     */
    loadExistingData() {
        // 从数据存储加载路单数据并渲染
        const beadData = dataStorage.getRoadmapData('beadRoad');
        beadData.forEach(result => {
            this.addToBeadRoad(result);
        });

        // 可以继续加载其他路单数据
    }

    /**
     * 添加游戏结果到路单
     * @param {Object} result 游戏结果
     */
    addResult(result) {
        this.addToBeadRoad(result);
        this.addToBigRoad(result);
        this.updateDerivedRoads();

        // 保存到数据存储
        dataStorage.updateRoadmap('beadRoad', result);
    }

    /**
     * 添加到珠盘路
     * @param {Object} result 游戏结果
     */
    addToBeadRoad(result) {
        const cell = Utils.createElement('div', {
            className: `bead-cell ${result.winner}`
        });

        // 设置显示内容
        let content = '';
        switch (result.winner) {
            case 'banker':
                content = '庄';
                break;
            case 'player':
                content = '闲';
                break;
            case 'tie':
                content = '和';
                break;
        }
        cell.textContent = content;

        this.elements.beadRoad.appendChild(cell);

        // 添加动画
        animationManager.animateRoadmapUpdate(cell);

        // 限制显示数量
        const cells = this.elements.beadRoad.children;
        if (cells.length > 36) { // 6x6网格
            this.elements.beadRoad.removeChild(cells[0]);
        }
    }

    /**
     * 添加到大路
     * @param {Object} result 游戏结果
     */
    addToBigRoad(result) {
        if (result.winner === 'tie') return; // 和局不记录在大路中

        const cell = Utils.createElement('div', {
            className: `big-road-cell ${result.winner}`
        });

        cell.textContent = result.winner === 'banker' ? '庄' : '闲';

        this.elements.bigRoad.appendChild(cell);

        // 添加动画
        animationManager.animateRoadmapUpdate(cell);

        // 限制显示数量
        const cells = this.elements.bigRoad.children;
        if (cells.length > 36) {
            this.elements.bigRoad.removeChild(cells[0]);
        }
    }

    /**
     * 更新衍生路单（小路、大眼仔路、曱甴路）
     */
    updateDerivedRoads() {
        // 这里可以实现复杂的衍生路单逻辑
        // 为了简化，暂时不实现完整的衍生路单算法
    }
}

// 创建全局UI管理实例
window.uiManager = new UIManager();
