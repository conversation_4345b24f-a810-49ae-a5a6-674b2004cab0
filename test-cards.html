<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扑克牌图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #0f4c3a;
        }
        
        .test-section {
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .test-card {
            width: 80px;
            height: 112px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding: 5px;
            font-size: 10px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        
        .card-back {
            background-image: url('PNG/blue_back.png');
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #0f4c3a;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1 style="color: white; text-align: center;">扑克牌PNG图片测试</h1>
    
    <div class="test-section">
        <h2>牌背测试</h2>
        <div class="card-grid">
            <div class="test-card card-back">蓝色牌背</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>A牌测试</h2>
        <div class="card-grid" id="ace-cards"></div>
    </div>
    
    <div class="test-section">
        <h2>人头牌测试</h2>
        <div class="card-grid" id="face-cards"></div>
    </div>
    
    <div class="test-section">
        <h2>数字牌测试（部分）</h2>
        <div class="card-grid" id="number-cards"></div>
    </div>

    <script>
        // 测试A牌
        const aceCards = ['AC', 'AD', 'AH', 'AS'];
        const aceContainer = document.getElementById('ace-cards');
        aceCards.forEach(card => {
            const cardDiv = document.createElement('div');
            cardDiv.className = 'test-card';
            cardDiv.style.backgroundImage = `url('PNG/${card}.png')`;
            cardDiv.textContent = card;
            aceContainer.appendChild(cardDiv);
        });
        
        // 测试人头牌
        const faceCards = ['JC', 'JD', 'JH', 'JS', 'QC', 'QD', 'QH', 'QS', 'KC', 'KD', 'KH', 'KS'];
        const faceContainer = document.getElementById('face-cards');
        faceCards.forEach(card => {
            const cardDiv = document.createElement('div');
            cardDiv.className = 'test-card';
            cardDiv.style.backgroundImage = `url('PNG/${card}.png')`;
            cardDiv.textContent = card;
            faceContainer.appendChild(cardDiv);
        });
        
        // 测试数字牌
        const numberCards = ['2C', '3D', '4H', '5S', '6C', '7D', '8H', '9S', '10C'];
        const numberContainer = document.getElementById('number-cards');
        numberCards.forEach(card => {
            const cardDiv = document.createElement('div');
            cardDiv.className = 'test-card';
            cardDiv.style.backgroundImage = `url('PNG/${card}.png')`;
            cardDiv.textContent = card;
            numberContainer.appendChild(cardDiv);
        });
    </script>
</body>
</html>
