# 百家乐游戏部署说明

## 本地运行

### 方法一：使用Python HTTP服务器
```bash
# 在项目根目录下运行
python -m http.server 8000

# 然后在浏览器中访问
http://localhost:8000
```

### 方法二：使用Node.js HTTP服务器
```bash
# 安装http-server
npm install -g http-server

# 在项目根目录下运行
http-server -p 8000

# 然后在浏览器中访问
http://localhost:8000
```

### 方法三：直接打开文件
由于游戏使用了本地存储，建议通过HTTP服务器运行，而不是直接打开HTML文件。

## 文件结构

```
baccarat2/
├── index.html              # 主游戏页面
├── test.html               # 测试页面
├── favicon.ico             # 网站图标
├── README.md               # 项目说明
├── DEPLOYMENT.md           # 部署说明
├── css/                    # 样式文件
│   ├── main.css           # 主样式
│   ├── table.css          # 牌桌样式
│   ├── cards.css          # 卡牌样式
│   └── animations.css     # 动画样式
└── js/                     # JavaScript文件
    ├── utils.js           # 工具函数
    ├── dataStorage.js     # 数据存储
    ├── gameLogic.js       # 游戏逻辑
    ├── stateManager.js    # 状态管理
    ├── animationManager.js # 动画管理
    ├── uiManager.js       # 界面管理
    └── main.js            # 主控制器
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 功能测试

1. 打开 `test.html` 进行功能测试
2. 点击各个测试按钮验证模块功能
3. 确认所有测试通过后，打开主游戏页面

## 生产环境部署

### 静态网站托管
游戏是纯前端应用，可以部署到任何静态网站托管服务：

- GitHub Pages
- Netlify
- Vercel
- 阿里云OSS
- 腾讯云COS

### 部署步骤
1. 将整个项目文件夹上传到托管服务
2. 设置 `index.html` 为默认首页
3. 确保所有文件路径正确
4. 测试游戏功能

## 自定义配置

### 修改初始余额
在 `js/dataStorage.js` 中修改：
```javascript
this.defaultData = {
    playerBalance: 10000, // 修改这里的值
    // ...
};
```

### 修改倒计时时间
在 `js/utils.js` 中修改：
```javascript
const GAME_CONSTANTS = {
    // ...
    BETTING_TIME: 15, // 修改这里的值（秒）
    // ...
};
```

### 修改筹码面值
在 `index.html` 中修改筹码元素的 `data-value` 属性。

## 故障排除

### 游戏无法启动
1. 检查浏览器控制台是否有错误信息
2. 确认所有JavaScript文件都正确加载
3. 检查浏览器是否支持本地存储

### 数据丢失
游戏数据存储在浏览器本地存储中，清除浏览器数据会导致游戏数据丢失。

### 动画卡顿
1. 检查浏览器性能
2. 关闭其他占用资源的标签页
3. 在低性能设备上可以考虑简化动画效果

## 开发说明

### 添加新功能
1. 在相应的模块文件中添加功能
2. 更新UI界面（如需要）
3. 在测试页面中添加测试用例
4. 更新文档

### 调试工具
在浏览器控制台中可以访问：
```javascript
// 查看游戏状态
BaccaratGame.controller.getGameSummary()

// 访问各个模块
BaccaratGame.logic    // 游戏逻辑
BaccaratGame.state    // 状态管理
BaccaratGame.storage  // 数据存储
BaccaratGame.ui       // 界面管理
```

## 性能优化

### 建议的优化措施
1. 启用GZIP压缩
2. 设置适当的缓存头
3. 压缩CSS和JavaScript文件
4. 优化图片资源（如果添加了图片）

### CDN加速
可以将静态资源部署到CDN以提高加载速度。

## 安全考虑

1. 游戏仅在客户端运行，不涉及服务器通信
2. 所有数据存储在本地，不会泄露用户信息
3. 建议在HTTPS环境下运行以确保安全

## 许可证

本项目仅供学习和演示使用，请勿用于商业用途。
