/**
 * 状态管理模块
 * 管理游戏的整体状态，包括游戏阶段、玩家状态、下注管理等
 */

class StateManager {
    constructor() {
        this.gameState = {
            phase: GAME_CONSTANTS.GAME_PHASES.BETTING,
            countdown: GAME_CONSTANTS.BETTING_TIME,
            isGameActive: false,
            currentRound: 0
        };

        this.playerState = {
            balance: 0,
            currentBets: {},
            totalBetAmount: 0,
            selectedChip: 100,
            canBet: true
        };

        this.gameResult = null;
        this.countdownTimer = null;
        this.eventListeners = {};
        this.isProcessingDeal = false; // 防止重复发牌

        this.initializeState();
    }

    /**
     * 初始化状态
     */
    initializeState() {
        this.refreshBalance();
        this.resetBets();
    }

    /**
     * 刷新余额（从数据存储同步）
     */
    refreshBalance() {
        this.playerState.balance = dataStorage.getPlayerBalance();
        console.log('余额已刷新:', this.playerState.balance);
    }

    /**
     * 添加事件监听器
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    off(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
        }
    }

    /**
     * 触发事件
     * @param {string} event 事件名称
     * @param {*} data 事件数据
     */
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * 开始新一轮游戏
     */
    startNewRound() {
        this.gameState.currentRound++;
        this.gameState.phase = GAME_CONSTANTS.GAME_PHASES.BETTING;
        this.gameState.countdown = GAME_CONSTANTS.BETTING_TIME;
        this.gameState.isGameActive = true;
        this.playerState.canBet = true;
        this.gameResult = null;
        this.isProcessingDeal = false; // 重置发牌标志

        this.resetBets();
        this.startCountdown();

        this.emit('roundStarted', {
            round: this.gameState.currentRound,
            phase: this.gameState.phase
        });
    }

    /**
     * 开始倒计时
     */
    startCountdown() {
        this.stopCountdown();

        this.countdownTimer = setInterval(() => {
            this.gameState.countdown--;

            this.emit('countdownUpdate', {
                countdown: this.gameState.countdown,
                phase: this.gameState.phase
            });

            if (this.gameState.countdown <= 0) {
                this.handleCountdownEnd();
            }
        }, 1000);
    }

    /**
     * 停止倒计时
     */
    stopCountdown() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    }

    /**
     * 处理倒计时结束
     */
    handleCountdownEnd() {
        this.stopCountdown();

        if (this.gameState.phase === GAME_CONSTANTS.GAME_PHASES.BETTING) {
            this.startDealing();
        }
    }

    /**
     * 开始发牌阶段
     */
    startDealing() {
        // 防止重复发牌
        if (this.isProcessingDeal || this.gameState.phase !== GAME_CONSTANTS.GAME_PHASES.BETTING) {
            console.warn('发牌已在进行中或当前阶段不允许发牌');
            return;
        }

        this.isProcessingDeal = true;
        this.gameState.phase = GAME_CONSTANTS.GAME_PHASES.DEALING;
        this.playerState.canBet = false;

        // 冻结下注金额
        this.freezeBets();

        this.emit('dealingStarted', {
            phase: this.gameState.phase,
            bets: Utils.deepClone(this.playerState.currentBets)
        });
    }

    /**
     * 开始揭牌阶段
     */
    startRevealing() {
        this.gameState.phase = GAME_CONSTANTS.GAME_PHASES.REVEALING;

        this.emit('revealingStarted', {
            phase: this.gameState.phase
        });
    }

    /**
     * 结束游戏并显示结果
     * @param {Object} gameResult 游戏结果
     */
    endGame(gameResult) {
        this.gameState.phase = GAME_CONSTANTS.GAME_PHASES.RESULT;
        this.gameState.isGameActive = false;
        this.isProcessingDeal = false; // 重置发牌标志
        this.gameResult = gameResult;

        // 计算派彩
        const payout = this.calculatePayout(gameResult);

        // 更新余额
        const balanceChange = payout - this.playerState.totalBetAmount;
        this.updateBalance(balanceChange);

        // 保存游戏记录
        this.saveGameRecord(gameResult, payout, balanceChange);

        this.emit('gameEnded', {
            phase: this.gameState.phase,
            result: gameResult,
            payout: payout,
            balanceChange: balanceChange,
            newBalance: this.playerState.balance
        });
    }

    /**
     * 计算派彩
     * @param {Object} gameResult 游戏结果
     * @returns {number} 派彩金额
     */
    calculatePayout(gameResult) {
        let totalPayout = 0;
        const bets = this.playerState.currentBets;
        const sideBets = gameLogic.calculateSideBets();

        // 主要下注派彩
        if (bets.banker && gameResult.winner === 'banker') {
            // 庄家6点获胜时赔率为0.5，其他情况为1
            const multiplier = gameResult.bankerPoints === 6 ? 0.5 : 1;
            totalPayout += bets.banker * (1 + multiplier);
        }

        if (bets.player && gameResult.winner === 'player') {
            totalPayout += bets.player * (1 + GAME_CONSTANTS.PAYOUTS.player);
        }

        if (bets.tie && gameResult.winner === 'tie') {
            totalPayout += bets.tie * (1 + GAME_CONSTANTS.PAYOUTS.tie);
        }

        // 边注派彩
        if (bets['banker-pair'] && sideBets.bankerPair) {
            totalPayout += bets['banker-pair'] * (1 + GAME_CONSTANTS.PAYOUTS['banker-pair']);
        }

        if (bets['player-pair'] && sideBets.playerPair) {
            totalPayout += bets['player-pair'] * (1 + GAME_CONSTANTS.PAYOUTS['player-pair']);
        }

        if (bets.lucky6 && sideBets.lucky6.win) {
            const multiplier = sideBets.lucky6.payout;
            totalPayout += bets.lucky6 * (1 + multiplier);
        }

        if (bets.lucky7 && sideBets.lucky7.win) {
            const multiplier = sideBets.lucky7.payout;
            totalPayout += bets.lucky7 * (1 + multiplier);
        }

        return totalPayout;
    }

    /**
     * 下注
     * @param {string} betType 下注类型
     * @param {number} amount 下注金额
     * @returns {boolean} 下注是否成功
     */
    placeBet(betType, amount) {
        if (!this.playerState.canBet) {
            Utils.showNotification('当前阶段不能下注', 'warning');
            return false;
        }

        if (amount <= 0) {
            Utils.showNotification('下注金额必须大于0', 'warning');
            return false;
        }

        const newTotalBet = this.playerState.totalBetAmount + amount;
        if (newTotalBet > this.playerState.balance) {
            Utils.showNotification('余额不足', 'warning');
            return false;
        }

        // 添加下注
        if (!this.playerState.currentBets[betType]) {
            this.playerState.currentBets[betType] = 0;
        }
        this.playerState.currentBets[betType] += amount;
        this.playerState.totalBetAmount += amount;

        this.emit('betPlaced', {
            betType: betType,
            amount: amount,
            totalBet: this.playerState.currentBets[betType],
            totalBetAmount: this.playerState.totalBetAmount,
            remainingBalance: this.playerState.balance - this.playerState.totalBetAmount
        });

        return true;
    }

    /**
     * 清除所有下注
     */
    clearAllBets() {
        if (!this.playerState.canBet) {
            Utils.showNotification('当前阶段不能清除下注', 'warning');
            return false;
        }

        this.resetBets();

        this.emit('betsCleared', {
            currentBets: this.playerState.currentBets,
            totalBetAmount: this.playerState.totalBetAmount
        });

        return true;
    }

    /**
     * 重置下注
     */
    resetBets() {
        this.playerState.currentBets = {};
        this.playerState.totalBetAmount = 0;
    }

    /**
     * 冻结下注（发牌时调用）
     */
    freezeBets() {
        // 从余额中扣除下注金额
        this.playerState.balance -= this.playerState.totalBetAmount;
        dataStorage.setPlayerBalance(this.playerState.balance);

        this.emit('betsFrozen', {
            frozenAmount: this.playerState.totalBetAmount,
            newBalance: this.playerState.balance
        });
    }

    /**
     * 更新余额
     * @param {number} change 余额变化
     */
    updateBalance(change) {
        this.playerState.balance += change;
        dataStorage.setPlayerBalance(this.playerState.balance);

        this.emit('balanceUpdated', {
            change: change,
            newBalance: this.playerState.balance
        });
    }

    /**
     * 设置选中的筹码
     * @param {number} chipValue 筹码面值
     */
    setSelectedChip(chipValue) {
        this.playerState.selectedChip = chipValue;

        this.emit('chipSelected', {
            chipValue: chipValue
        });
    }

    /**
     * 保存游戏记录
     * @param {Object} gameResult 游戏结果
     * @param {number} payout 派彩
     * @param {number} balanceChange 余额变化
     */
    saveGameRecord(gameResult, payout, balanceChange) {
        const record = {
            ...gameResult,
            bets: Utils.deepClone(this.playerState.currentBets),
            payout: payout,
            balanceChange: balanceChange
        };

        dataStorage.addGameHistory(record);
        dataStorage.updateStatistics(record);
    }

    /**
     * 获取当前游戏状态
     * @returns {Object} 游戏状态
     */
    getGameState() {
        return Utils.deepClone(this.gameState);
    }

    /**
     * 获取玩家状态
     * @returns {Object} 玩家状态
     */
    getPlayerState() {
        return Utils.deepClone(this.playerState);
    }

    /**
     * 获取游戏结果
     * @returns {Object} 游戏结果
     */
    getGameResult() {
        return this.gameResult ? Utils.deepClone(this.gameResult) : null;
    }

    /**
     * 检查是否可以开始新游戏
     * @returns {boolean} 是否可以开始
     */
    canStartNewGame() {
        return !this.gameState.isGameActive && this.playerState.balance > 0;
    }

    /**
     * 重置状态管理器
     */
    reset() {
        this.stopCountdown();
        this.gameState = {
            phase: GAME_CONSTANTS.GAME_PHASES.BETTING,
            countdown: GAME_CONSTANTS.BETTING_TIME,
            isGameActive: false,
            currentRound: 0
        };
        this.initializeState();
        this.gameResult = null;
    }
}

// 创建全局状态管理实例
window.stateManager = new StateManager();
