/**
 * 主控制器
 * 协调各个模块，控制游戏流程
 */

class GameController {
    constructor() {
        this.isInitialized = false;
        this.currentGameData = null;
    }
    
    /**
     * 初始化游戏
     */
    async initialize() {
        try {
            console.log('正在初始化百家乐游戏...');
            
            // 检查必要的模块是否已加载
            this.checkModules();
            
            // 初始化UI
            uiManager.initialize();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 显示欢迎信息
            this.showWelcomeMessage();
            
            this.isInitialized = true;
            console.log('游戏初始化完成');
            
            // 自动开始第一轮游戏
            await Utils.delay(1000);
            this.startNewRound();
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            Utils.showNotification('游戏初始化失败，请刷新页面重试', 'error');
        }
    }
    
    /**
     * 检查必要模块是否已加载
     */
    checkModules() {
        const requiredModules = [
            'Utils', 'GAME_CONSTANTS', 'dataStorage', 
            'gameLogic', 'stateManager', 'animationManager', 'uiManager'
        ];
        
        const missingModules = requiredModules.filter(module => !window[module]);
        
        if (missingModules.length > 0) {
            throw new Error(`缺少必要模块: ${missingModules.join(', ')}`);
        }
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 监听开始新一轮事件
        window.addEventListener('startNewRound', () => {
            this.startNewRound();
        });
        
        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardInput(e);
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleWindowResize();
        }, 250));
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 监听页面卸载
        window.addEventListener('beforeunload', () => {
            this.handlePageUnload();
        });
    }
    
    /**
     * 显示欢迎信息
     */
    showWelcomeMessage() {
        const balance = dataStorage.getPlayerBalance();
        const message = `欢迎来到百家乐游戏！当前余额：${Utils.formatCurrency(balance)}`;
        Utils.showNotification(message, 'success', 3000);
    }
    
    /**
     * 开始新一轮游戏
     */
    async startNewRound() {
        try {
            if (!this.isInitialized) {
                console.warn('游戏尚未初始化完成');
                return;
            }
            
            // 检查是否可以开始新游戏
            if (!stateManager.canStartNewGame()) {
                Utils.showNotification('无法开始新游戏：余额不足或游戏正在进行中', 'warning');
                return;
            }
            
            console.log('开始新一轮游戏');
            
            // 重置游戏逻辑
            gameLogic.resetGame();
            
            // 开始新游戏
            this.currentGameData = gameLogic.startNewGame();
            
            // 启动状态管理器的新一轮
            stateManager.startNewRound();
            
        } catch (error) {
            console.error('开始新游戏失败:', error);
            Utils.showNotification('开始新游戏失败，请重试', 'error');
        }
    }
    
    /**
     * 开始发牌流程
     */
    async startDealingProcess() {
        try {
            console.log('开始发牌流程');
            
            // 切换到发牌阶段
            stateManager.startDealing();
            
            // 发初始四张牌
            gameLogic.dealInitialCards();
            
            // 执行发牌动画
            await this.animateInitialDeal();
            
            // 检查是否为自然赢
            if (this.currentGameData.isNatural) {
                console.log('自然赢，直接结束游戏');
                await this.revealAllCards();
                this.endGame();
            } else {
                // 执行补牌规则
                await this.executeDrawingRules();
            }
            
        } catch (error) {
            console.error('发牌流程失败:', error);
            Utils.showNotification('发牌失败，请重试', 'error');
        }
    }
    
    /**
     * 执行初始发牌动画
     */
    async animateInitialDeal() {
        const gameData = gameLogic.getCurrentGame();
        
        // 按照百家乐规则发牌：闲家第一张，庄家第一张，闲家第二张，庄家第二张
        const dealOrder = [
            { position: 'player', cardIndex: 0, card: gameData.playerCards[0] },
            { position: 'banker', cardIndex: 0, card: gameData.bankerCards[0] },
            { position: 'player', cardIndex: 1, card: gameData.playerCards[1] },
            { position: 'banker', cardIndex: 1, card: gameData.bankerCards[1] }
        ];
        
        for (const deal of dealOrder) {
            await uiManager.dealCardTo(deal.card, deal.position, deal.cardIndex);
            await Utils.delay(300); // 发牌间隔
        }
        
        console.log('初始发牌完成');
    }
    
    /**
     * 执行补牌规则
     */
    async executeDrawingRules() {
        try {
            console.log('执行补牌规则');
            
            // 切换到揭牌阶段
            stateManager.startRevealing();
            
            // 先翻开前两张牌
            await this.revealInitialCards();
            
            // 执行补牌逻辑
            const gameDataBefore = Utils.deepClone(gameLogic.getCurrentGame());
            gameLogic.executeDrawingRules();
            const gameDataAfter = gameLogic.getCurrentGame();
            
            // 如果有新牌，执行发牌动画
            if (gameDataAfter.playerCards.length > gameDataBefore.playerCards.length) {
                const newCard = gameDataAfter.playerCards[gameDataAfter.playerCards.length - 1];
                await uiManager.dealCardTo(newCard, 'player', gameDataAfter.playerCards.length - 1);
                await Utils.delay(500);
            }
            
            if (gameDataAfter.bankerCards.length > gameDataBefore.bankerCards.length) {
                const newCard = gameDataAfter.bankerCards[gameDataAfter.bankerCards.length - 1];
                await uiManager.dealCardTo(newCard, 'banker', gameDataAfter.bankerCards.length - 1);
                await Utils.delay(500);
            }
            
            // 翻开所有牌
            await this.revealAllCards();
            
            // 结束游戏
            this.endGame();
            
        } catch (error) {
            console.error('补牌规则执行失败:', error);
            Utils.showNotification('补牌失败，请重试', 'error');
        }
    }
    
    /**
     * 翻开初始两张牌
     */
    async revealInitialCards() {
        const bankerCards = uiManager.elements.bankerCards.querySelectorAll('.card');
        const playerCards = uiManager.elements.playerCards.querySelectorAll('.card');
        
        // 翻开闲家前两张牌
        for (let i = 0; i < Math.min(2, playerCards.length); i++) {
            await uiManager.revealCard(playerCards[i]);
            await Utils.delay(300);
        }
        
        // 更新闲家点数
        const gameData = gameLogic.getCurrentGame();
        uiManager.updatePoints('player', gameData.playerPoints);
        
        await Utils.delay(500);
        
        // 翻开庄家前两张牌
        for (let i = 0; i < Math.min(2, bankerCards.length); i++) {
            await uiManager.revealCard(bankerCards[i]);
            await Utils.delay(300);
        }
        
        // 更新庄家点数
        uiManager.updatePoints('banker', gameData.bankerPoints);
    }
    
    /**
     * 翻开所有牌
     */
    async revealAllCards() {
        const bankerCards = uiManager.elements.bankerCards.querySelectorAll('.card:not(.flipped)');
        const playerCards = uiManager.elements.playerCards.querySelectorAll('.card:not(.flipped)');
        
        // 翻开所有未翻开的牌
        const allUnrevealedCards = [...playerCards, ...bankerCards];
        
        for (const card of allUnrevealedCards) {
            await uiManager.revealCard(card);
            await Utils.delay(200);
        }
        
        // 更新最终点数
        const gameData = gameLogic.getCurrentGame();
        uiManager.updatePoints('player', gameData.playerPoints);
        uiManager.updatePoints('banker', gameData.bankerPoints);
    }
    
    /**
     * 结束游戏
     */
    endGame() {
        try {
            const gameData = gameLogic.getCurrentGame();
            console.log('游戏结束，结果:', gameData);
            
            // 更新当前游戏数据
            this.currentGameData = gameData;
            
            // 通知状态管理器游戏结束
            stateManager.endGame(gameData);
            
            // 播放结果音效
            if (dataStorage.getSetting('soundEnabled')) {
                Utils.playSound('gameEnd');
            }
            
        } catch (error) {
            console.error('结束游戏失败:', error);
            Utils.showNotification('游戏结束处理失败', 'error');
        }
    }
    
    /**
     * 处理键盘输入
     * @param {KeyboardEvent} event 键盘事件
     */
    handleKeyboardInput(event) {
        // 如果模态框打开，ESC键关闭模态框
        if (event.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal:not(.hidden)');
            openModals.forEach(modal => {
                uiManager.hideModal(modal);
            });
        }
        
        // 数字键选择筹码
        const chipValues = [10, 50, 100, 500, 1000];
        const keyIndex = parseInt(event.key) - 1;
        if (keyIndex >= 0 && keyIndex < chipValues.length) {
            stateManager.setSelectedChip(chipValues[keyIndex]);
        }
        
        // 空格键清除下注
        if (event.key === ' ') {
            event.preventDefault();
            stateManager.clearAllBets();
        }
    }
    
    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 这里可以添加响应式布局调整逻辑
        console.log('窗口大小已变化');
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停游戏
            console.log('页面隐藏，暂停游戏');
        } else {
            // 页面显示时恢复游戏
            console.log('页面显示，恢复游戏');
        }
    }
    
    /**
     * 处理页面卸载
     */
    handlePageUnload() {
        // 保存游戏状态
        console.log('页面卸载，保存游戏状态');
    }
    
    /**
     * 获取游戏状态摘要
     * @returns {Object} 游戏状态摘要
     */
    getGameSummary() {
        return {
            isInitialized: this.isInitialized,
            currentGame: this.currentGameData,
            gameState: stateManager.getGameState(),
            playerState: stateManager.getPlayerState(),
            deckInfo: gameLogic.getDeckInfo(),
            dataSummary: dataStorage.getDataSummary()
        };
    }
}

// 创建全局游戏控制器实例
window.gameController = new GameController();

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await gameController.initialize();
        
        // 监听状态管理器的发牌开始事件
        stateManager.on('dealingStarted', () => {
            gameController.startDealingProcess();
        });
        
    } catch (error) {
        console.error('游戏启动失败:', error);
        Utils.showNotification('游戏启动失败，请刷新页面重试', 'error');
    }
});

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
    Utils.showNotification('发生未知错误，请刷新页面', 'error');
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    Utils.showNotification('发生异步错误，请刷新页面', 'error');
});

// 导出游戏控制器供调试使用
window.BaccaratGame = {
    controller: gameController,
    utils: Utils,
    constants: GAME_CONSTANTS,
    storage: dataStorage,
    logic: gameLogic,
    state: stateManager,
    animation: animationManager,
    ui: uiManager
};
