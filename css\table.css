/* 牌桌区域样式 */
.table-section {
    background: linear-gradient(135deg, #1a5f4a, #0f4c3a);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

/* 卡牌区域 */
.card-area {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.banker-area,
.player-area {
    text-align: center;
    flex: 1;
    max-width: 300px;
}

.banker-area h3,
.player-area h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    font-weight: bold;
}

.card-slots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    min-height: 120px;
    align-items: center;
}

.card-slot {
    width: 80px;
    height: 112px;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.card-slot.occupied {
    border: 2px solid rgba(255, 215, 0, 0.5);
    background: rgba(255, 215, 0, 0.1);
}

.points {
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: inline-block;
    min-width: 60px;
}

/* 下注区域 */
.betting-area {
    margin-bottom: 25px;
}

.main-bets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.side-bets {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.bet-zone {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bet-zone:hover {
    border-color: rgba(255, 215, 0, 0.5);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
}

.bet-zone.active {
    border-color: #ffd700;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.bet-zone.has-bet {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
}

.bet-label {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 5px;
}

.bet-odds {
    font-size: 12px;
    color: #cccccc;
    margin-bottom: 8px;
}

.bet-amount {
    font-size: 18px;
    font-weight: bold;
    color: #ffd700;
    background: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 筹码选择区 */
.chip-selection {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.chip {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid;
    position: relative;
    overflow: hidden;
}

.chip[data-value="10"] {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-color: #ff5252;
    color: white;
}

.chip[data-value="50"] {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    border-color: #26a69a;
    color: white;
}

.chip[data-value="100"] {
    background: linear-gradient(135deg, #45b7d1, #3498db);
    border-color: #2980b9;
    color: white;
}

.chip[data-value="500"] {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-color: #d35400;
    color: white;
}

.chip[data-value="1000"] {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    border-color: #7d3c98;
    color: white;
}

.chip:hover {
    transform: scale(1.1) translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.chip.selected {
    transform: scale(1.15);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    border-color: #ffd700;
}

.chip::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
    pointer-events: none;
}

.clear-btn {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-area {
        flex-direction: column;
        gap: 20px;
    }
    
    .main-bets {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .side-bets {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .chip-selection {
        gap: 10px;
    }
    
    .chip {
        width: 50px;
        height: 50px;
        font-size: 12px;
    }
    
    .bet-zone {
        padding: 10px;
    }
    
    .bet-label {
        font-size: 14px;
    }
    
    .bet-odds {
        font-size: 10px;
    }
    
    .bet-amount {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .table-section {
        padding: 15px;
    }
    
    .card-slots {
        gap: 5px;
    }
    
    .card-slot {
        width: 60px;
        height: 84px;
    }
    
    .side-bets {
        grid-template-columns: 1fr;
    }
    
    .chip {
        width: 45px;
        height: 45px;
        font-size: 11px;
    }
}
