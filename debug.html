<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百家乐游戏调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .debug-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }
        .debug-button:hover {
            background: #1976D2;
        }
        .debug-button.danger {
            background: #f44336;
        }
        .debug-button.danger:hover {
            background: #d32f2f;
        }
        .debug-button.success {
            background: #4CAF50;
        }
        .debug-button.success:hover {
            background: #45a049;
        }
        .debug-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .status-label {
            font-weight: bold;
            color: #333;
        }
        .status-value {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🎮 百家乐游戏调试工具</h1>
        
        <div class="debug-section">
            <h2>🔍 当前状态</h2>
            <div id="current-status"></div>
            <button class="debug-button" onclick="refreshStatus()">刷新状态</button>
        </div>
        
        <div class="debug-section">
            <h2>💰 余额管理</h2>
            <button class="debug-button success" onclick="resetBalance()">重置余额到10000</button>
            <button class="debug-button success" onclick="addBalance(5000)">增加5000</button>
            <button class="debug-button success" onclick="addBalance(10000)">增加10000</button>
            <button class="debug-button" onclick="setCustomBalance()">设置自定义余额</button>
        </div>
        
        <div class="debug-section">
            <h2>🎲 游戏控制</h2>
            <button class="debug-button" onclick="simulateGame()">模拟一局游戏</button>
            <button class="debug-button" onclick="resetGameState()">重置游戏状态</button>
            <button class="debug-button" onclick="forceStartGame()">强制开始游戏</button>
        </div>
        
        <div class="debug-section">
            <h2>💾 数据管理</h2>
            <button class="debug-button" onclick="exportData()">导出数据</button>
            <button class="debug-button" onclick="importData()">导入数据</button>
            <button class="debug-button danger" onclick="clearAllData()">清除所有数据</button>
        </div>
        
        <div class="debug-section">
            <h2>🔧 快速操作</h2>
            <button class="debug-button" onclick="openGame()">打开游戏</button>
            <button class="debug-button" onclick="openTest()">打开测试页面</button>
            <button class="debug-button" onclick="checkLocalStorage()">检查本地存储</button>
        </div>
        
        <div class="debug-section">
            <h2>📊 调试输出</h2>
            <div id="debug-output" class="debug-output">等待调试信息...</div>
            <button class="debug-button" onclick="clearOutput()">清除输出</button>
        </div>
    </div>

    <!-- 加载游戏模块 -->
    <script src="js/utils.js"></script>
    <script src="js/dataStorage.js"></script>
    <script src="js/gameLogic.js"></script>
    <script src="js/stateManager.js"></script>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f44' : type === 'success' ? '#4f4' : type === 'warning' ? '#ff4' : '#0f0';
            output.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('debug-output').innerHTML = '';
        }

        function refreshStatus() {
            try {
                const balance = dataStorage.getPlayerBalance();
                const gameState = stateManager.getGameState();
                const playerState = stateManager.getPlayerState();
                const stats = dataStorage.getStatistics();
                const deckInfo = gameLogic.getDeckInfo();

                const statusHtml = `
                    <div class="status-item">
                        <span class="status-label">数据存储余额:</span>
                        <span class="status-value">${Utils.formatCurrency(balance)}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">状态管理器余额:</span>
                        <span class="status-value">${Utils.formatCurrency(playerState.balance)}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">游戏阶段:</span>
                        <span class="status-value">${gameState.phase}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">游戏活跃:</span>
                        <span class="status-value">${gameState.isGameActive ? '是' : '否'}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">当前下注总额:</span>
                        <span class="status-value">${Utils.formatCurrency(playerState.totalBetAmount)}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">总游戏数:</span>
                        <span class="status-value">${stats.totalGames}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">剩余牌数:</span>
                        <span class="status-value">${deckInfo.remainingCards}</span>
                    </div>
                `;

                document.getElementById('current-status').innerHTML = statusHtml;
                log('状态已刷新', 'success');
            } catch (error) {
                log(`刷新状态失败: ${error.message}`, 'error');
            }
        }

        function resetBalance() {
            try {
                dataStorage.setPlayerBalance(10000);
                stateManager.refreshBalance();
                log('余额已重置为10000', 'success');
                refreshStatus();
            } catch (error) {
                log(`重置余额失败: ${error.message}`, 'error');
            }
        }

        function addBalance(amount) {
            try {
                dataStorage.updatePlayerBalance(amount);
                stateManager.refreshBalance();
                log(`成功增加${Utils.formatCurrency(amount)}余额`, 'success');
                refreshStatus();
            } catch (error) {
                log(`增加余额失败: ${error.message}`, 'error');
            }
        }

        function setCustomBalance() {
            const amount = prompt('请输入新的余额金额:', '10000');
            if (amount && !isNaN(amount)) {
                const newBalance = parseInt(amount);
                if (newBalance >= 0) {
                    try {
                        dataStorage.setPlayerBalance(newBalance);
                        stateManager.refreshBalance();
                        log(`余额已设置为${Utils.formatCurrency(newBalance)}`, 'success');
                        refreshStatus();
                    } catch (error) {
                        log(`设置余额失败: ${error.message}`, 'error');
                    }
                } else {
                    log('余额不能为负数', 'warning');
                }
            }
        }

        function simulateGame() {
            try {
                log('开始模拟游戏...', 'info');
                
                // 重置游戏状态
                stateManager.reset();
                gameLogic.resetGame();
                
                // 开始新游戏
                const game = gameLogic.startNewGame();
                stateManager.startNewRound();
                
                // 模拟下注
                stateManager.setSelectedChip(100);
                stateManager.placeBet('banker', 100);
                
                // 发牌
                gameLogic.dealInitialCards();
                const gameData = gameLogic.getCurrentGame();
                log(`发牌完成: 庄家${gameData.bankerPoints}点, 闲家${gameData.playerPoints}点`, 'info');
                
                // 补牌
                if (!gameData.isNatural) {
                    gameLogic.executeDrawingRules();
                }
                
                // 结算
                const finalData = gameLogic.getCurrentGame();
                stateManager.endGame(finalData);
                
                log(`游戏结束: ${finalData.winner}获胜`, 'success');
                refreshStatus();
            } catch (error) {
                log(`模拟游戏失败: ${error.message}`, 'error');
            }
        }

        function resetGameState() {
            try {
                stateManager.reset();
                gameLogic.resetGame();
                log('游戏状态已重置', 'success');
                refreshStatus();
            } catch (error) {
                log(`重置游戏状态失败: ${error.message}`, 'error');
            }
        }

        function forceStartGame() {
            try {
                if (stateManager.canStartNewGame()) {
                    stateManager.startNewRound();
                    log('游戏已强制开始', 'success');
                } else {
                    log('无法开始游戏: 余额不足或游戏正在进行', 'warning');
                }
                refreshStatus();
            } catch (error) {
                log(`强制开始游戏失败: ${error.message}`, 'error');
            }
        }

        function exportData() {
            try {
                const data = dataStorage.exportData();
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `baccarat_data_${new Date().toISOString().slice(0, 10)}.json`;
                a.click();
                URL.revokeObjectURL(url);
                log('数据导出成功', 'success');
            } catch (error) {
                log(`导出数据失败: ${error.message}`, 'error');
            }
        }

        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const success = dataStorage.importData(e.target.result);
                            if (success) {
                                stateManager.refreshBalance();
                                log('数据导入成功', 'success');
                                refreshStatus();
                            } else {
                                log('数据导入失败', 'error');
                            }
                        } catch (error) {
                            log(`导入数据失败: ${error.message}`, 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function clearAllData() {
            if (confirm('确定要清除所有游戏数据吗？此操作不可恢复！')) {
                try {
                    dataStorage.resetAllData();
                    stateManager.reset();
                    log('所有数据已清除', 'success');
                    refreshStatus();
                } catch (error) {
                    log(`清除数据失败: ${error.message}`, 'error');
                }
            }
        }

        function openGame() {
            window.open('index.html', '_blank');
        }

        function openTest() {
            window.open('test.html', '_blank');
        }

        function checkLocalStorage() {
            try {
                const keys = Object.keys(localStorage);
                log(`本地存储包含 ${keys.length} 个键:`, 'info');
                keys.forEach(key => {
                    const value = localStorage.getItem(key);
                    log(`  ${key}: ${value.length} 字符`, 'info');
                });
                
                const gameData = localStorage.getItem('baccarat_game_data');
                if (gameData) {
                    const parsed = JSON.parse(gameData);
                    log(`游戏数据余额: ${parsed.playerBalance}`, 'info');
                } else {
                    log('未找到游戏数据', 'warning');
                }
            } catch (error) {
                log(`检查本地存储失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动刷新状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                refreshStatus();
                log('调试工具已加载', 'success');
            }, 500);
        });
    </script>
</body>
</html>
